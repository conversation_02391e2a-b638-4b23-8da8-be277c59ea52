import express from "express";

import { commentAnalytics } from "./analytics/commentAnalytics.js";
import { videoAnalytics } from "./analytics/videoAnalytics.js";
import { profileAnalytics } from "./analytics/profileAnalytics.js"


const app = express();
app.use(express.json());


/**
 * Dynamic Lead Analysis Model
 */
app.post('/api/v1/analytics', async (req, res) => {

    const { description, comments = [], hashTagVideos = [], w_1, w_2, w_3, w_4 } = req.body;
    console.log(`111`)

    if (!description || (comments.length === 0 && hashTagVideos.length === 0)) {
        return res.status(400).json({
            error: "description is required, and at least one of comments and hashTagVideos must not be empty."
        });
    }

    const commentResults = [];
    if (comments.length > 0) {
        for (let comment of comments) {
            const commentTime = comment.CommentTime ?
                Math.floor(new Date(comment.CommentTime).getTime() / 1000) :
                null;
            if (!comment || typeof comment !== 'object' || !comment.CommentTime) continue;
            const { result, intentScore, timeScore, geographicMatchScore, scenarioReinforcement } = await commentAnalytics(description, comment, commentTime)
            // console.log('SLXu-intentScore: ', intentScore);
            // console.log('SLXu-timeScore: ', timeScore);
            // console.log('SLXu-geographicMatchScore: ', geographicMatchScore);
            if (result === "HIGH_CONFIDENCE" || result === "SEND_TO_LLM") {
                const { accountScore, labels, urls } = await profileAnalytics(comment.profile)
                // console.log('SLXu-accountScore: ', accountScore);
                // console.log('SLXu-labels: ', labels);
                // console.log('SLXu-urls: ', urls);
                const finalScore =
                    (w_1 ?? 0.4) * (intentScore ?? 0) +
                    (w_2 ?? 0.3) * (accountScore ?? 0) +
                    (w_3 ?? 0.2) * (timeScore ?? 0) +
                    (w_4 ?? 0.1) * (geographicMatchScore ?? 0);
                console.log('SLXu-finalScore:', finalScore);
                commentResults.push({
                    videoUrl: comment?.VideoUrl ?? "",
                    text: comment?.CommentText ?? "",
                    intentScore,
                    labels,
                    urls,
                    finalScore,
                    scenarioReinforcement // Scenario reinforcement for Compliance Lead Generation Template
                });
            }
        }
        commentResults.sort((a, b) => b.finalScore - a.finalScore);
    }


    const kocResults = [];
    if (hashTagVideos.length > 0) {
        for (let video of hashTagVideos) {
            if (!video || typeof video !== 'object' || !video.createTime) continue;
            const { intentScore, timeScore, geographicMatchScore, scenarioReinforcement } = await videoAnalytics(description, video)
            const { accountScore, labels, urls } = await profileAnalytics(video.profile);
            const finalScore =
                (w_1 ?? 0.4) * (intentScore ?? 0) +
                (w_2 ?? 0.3) * (accountScore ?? 0) +
                (w_3 ?? 0.2) * (timeScore ?? 0) +
                (w_4 ?? 0.1) * (geographicMatchScore ?? 0);
            kocResults.push({
                videoUrl: video?.videoWebUrl ?? "",
                intentScore,
                labels,
                urls,
                finalScore,
                scenarioReinforcement // Scenario reinforcement for Compliance Lead Generation Template
            });
        }
        kocResults.sort((a, b) => b.finalScore - a.finalScore);
    }


    res.json({ sortedComments: commentResults, sortedKocs: kocResults });
});


app.listen(3000, () => {
    console.log('Server is running on port 3000');
});