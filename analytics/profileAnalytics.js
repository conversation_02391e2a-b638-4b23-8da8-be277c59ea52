// Analyze profile
import <PERSON><PERSON><PERSON> from "openai";
import dotenv from "dotenv";
import fs from 'fs';

dotenv.config();
const openAI = new OpenAI({
    apiKey: process.env.DEEPSEEK_API_KEY,
    baseURL: "https://api.deepseek.com",
});

const instructions = `
You are an expert market analyst specializing in identifying key commercial interests and extracting active product-pushing content from social media account profiles. Your primary focus is on accounts with fewer than 1000 followers, recognizing that their commercial collaborations often occur off-platform. Your task is to analyze raw account data and provide a concise summary. The current date for calculating "last 30 days" is **current timestamp**.

---

**Analysis Requirements:**

1.  **Account Key Metrics:**
    * **Follower Count:** Extract the total number of followers for the account.
    * **Total Likes (Hearts):** Extract the total number of likes (hearts) the account has received across all its videos.
    * **Identified Commercial Collaborations Count:** Count the total number of distinct videos in the provided data that strongly indicate a commercial collaboration. **Criteria for Commercial Collaboration:**
        * The 'IsAd' field is 'true' OR the 'IsSponsored' field is 'true'.
        * OR, the video's 'text' field contains explicit brand mentions (e.g., "@BrandName") *and* clearly promotional phrases or call-to-actions like "code", "link in bio", "shop now", "discount", "my favorite product".
        * OR, the 'authorMeta.bioLink' is non-null AND the video's 'text' encourages viewers to "link in bio" for a product.
        **Each unique video meeting any of these criteria should be counted as one collaboration instance.**

2.  **Commercial Interests:**
    * Identify and list up to **three (3)** primary commercial interests of the account.
    * Base this on the Username, Nickname, Video Text (especially hashtags and product mentions), 'signature', and any 'Input' fields in the provided data.
    * **Prioritize explicit indicators** (e.g., "gaming" if the username is "gamerXYZ" and hashtags include #gaming).
    * **Also infer interests from strong product mentions/promotions** (e.g., mentions of "Gymshark" and "ehplabs" indicate "fitness" or "supplements").
    * If fewer than three distinct commercial interests are clearly identifiable, list only those that are evident.

3.  **Active Product-Pushing Videos (Last 30 Days):**
    * Identify all videos that qualify as "product-pushing" (带货) and were created within the last **30 days** from **current timestamp**.
    * **Criteria for "Product-Pushing":**
        * The 'IsAd' field is 'true'.
        * OR the 'IsSponsored' field is 'true'.
        * OR, the video's 'text' field contains explicit product links, brand mentions (e.g., "@BrandName") *and* promotional keywords (e.g., "code", "link in bio", "shop now", "discount", "my favorite product").
        * OR, a non-null 'authorMeta.bioLink' is present and the video 'text' explicitly directs users to "link in bio to shop".
    * **Criteria for "Active" (within 30 days):** The 'CreateTime' of the video must be within the last 30 days relative to the **current timestamp**.
    * For each qualifying video, extract and list its 'webVideoUrl'.

---

**Please output the result strictly in the following format:**

**Fans: [Your determined fans number]**
**Hearts: [Your determined hearts]**
**IdentifiedCommercialCollaborationsCount: [Your determined Commerical collaborations count]**
**CommercialInterests: [Your determined Commerical interests, at most 3]**
**ActiveProductPushingVideosLast30Days: [Your determined Active Product-Pushing Videos urls (Last 30 Days)]**

---

**Now, analyze the profile:**

Profile:
`

/**
 * 计算账号质量分
 * @param {number} fans 粉丝数
 * @param {number} hearts 点赞数
 * @param {number} commercialCollaborationsCount 商业合作历史条数
 * @returns {number} 账号质量分
 */
function calculateAccountScore(fans, hearts, commercialCollaborationsCount) {
    let score = 0;

    if (fans < 1000) {
        score = 30;
    }

    if (fans > 0 && (hearts / fans) > 0.15) {
        score += 20;
    }

    if (commercialCollaborationsCount > 3) {
        score += 25;
    }

    return score;
}

export async function profileAnalytics(profile) {
    const completion = await openAI.chat.completions.create({
        model: "deepseek-reasoner",
        messages: [{
            role: "system",
            content: instructions
        }, {
            role: "user",
            content: `Profile: ${JSON.stringify(profile)}`
        }]
    });

    const output = completion.choices[0].message.content;

    const fansMatch = output.match(/Fans:\s*\[?(\d+)\]?/i);
    const fans = fansMatch ? Number(fansMatch[1]) : 0;

    const heartsMatch = output.match(/Hearts:\s*\[?(\d+)\]?/i);
    const hearts = heartsMatch ? Number(heartsMatch[1]) : 0;

    const collabMatch = output.match(/IdentifiedCommercialCollaborationsCount:\s*\[?(\d+)\]?/i);
    const identifiedCommercialCollaborationsCount = collabMatch ? Number(collabMatch[1]) : 0;

    const interestsMatch = output.match(/CommercialInterests:\s*(\[[^\]]*\])/i);
    let commercialInterests = [];
    try {
        commercialInterests = interestsMatch ? JSON.parse(interestsMatch[1]) : [];
    } catch { commercialInterests = []; }
    // console.log('SLXu-commercialInterests:', commercialInterests)

    const activeVideosMatch = output.match(/ActiveProductPushingVideosLast30Days:\s*(\[[^\]]*\])/i);
    let activeVideos = [];
    try {
        activeVideos = activeVideosMatch ? JSON.parse(activeVideosMatch[1]) : [];
    } catch { activeVideos = []; }
    // console.log('SLXu-activeVideos:', activeVideos)

    const accountScore = calculateAccountScore(fans, hearts, identifiedCommercialCollaborationsCount)

    return { accountScore, commercialInterests, activeVideos } // 账号质量分，商业兴趣，近期活跃带货视频

}