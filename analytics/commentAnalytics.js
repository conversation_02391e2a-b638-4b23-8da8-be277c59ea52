// Analyze comment
import <PERSON>A<PERSON> from "openai";
import dotenv from "dotenv";


dotenv.config();
const openAI = new OpenAI({
    apiKey: process.env.DEEPSEEK_API_KEY,
    baseURL: "https://api.deepseek.com",
});

/**
 * Construct a prompt to deal with comment's context
 * @returns prompt
 */
function textPrompt(description, source) {
    const itemDescription = description;
    const commentSource = source; // "Our Own Video" or "Competitor Video"

    const instructions = `
    You are an expert market analyst, specialized in identifying and extracting high-conversion potential leads. Your task is to analyze user comments on TikTok ad videos, accurately identifying their underlying purchase intent and high-value needs. Crucially, you must extract any exposed user pain points, gain points, or specific areas of concern from the comments, and refine them into "scenario reinforcement" information that sales personnel can use to craft personalized outreach. Additionally, you need to calculate a "Geographic Match Score" based on any location-specific inquiries in the comment and the geographical information available in the product description.

    ---

    **[Dynamic Insertion Area: Product/Tool Description]**
    
    Here is key information about the product/tool we are analyzing:
    ${itemDescription}
    **(Note: This description may contain geographical information such as service areas, store addresses, or location-based discount codes.)**

    ---

    **[Dynamic Insertion Area: Comment Source Specific Instructions]**
    Here is the source of the comment you are analyzing :
    ${commentSource}

    **If the comment is from "Our Own Video":**
    Please focus on identifying explicit purchase intent, specific inquiries, or clear needs related to our product.

    **If the comment is from "Competitor Video":**
    Please prioritize identifying user dissatisfaction with the competitor, demands for missing features, or inquiries about alternative solutions. These are all considered high-conversion potential for us.

    ---

    **Intent Categories (Choose strictly one as the result):**

    **High-Conversion Potential Intents:**
    * **Purchase Intent:** User explicitly expresses desire to buy, asks about purchasing methods, payment, price (if own video comment). Or expresses dissatisfaction with competitor pricing and seeks alternatives (if competitor video comment).
    * **Product/Feature Match Need:** User describes a specific need or pain point and asks if the product/tool offers a specific solution or contains a core feature/characteristic.
    * **Trial/Acquisition Intent:** User asks how to trial, about free versions, downloads, registration, or how to get the product/tool (common for SaaS).
    * **Seeking Alternative/Competitor Dissatisfaction (Competitor Comments ONLY):** User expresses strong negative sentiment towards a competitor, complains about its flaws, or explicitly asks for better alternatives.
    * **Specific Size/Spec Inquiry (E-commerce ONLY):** User asks about the availability of a specific size, color, model, or style, typically a final confirmation before purchase.

    **Other Intents (Not high-conversion potential, but still identified):**
    * **General Inquiry/Curiosity:** User asks general questions or expresses curiosity, but without clear purchase or high-value intent.
    * **Emotional/Appreciation/Irrelevant:** Comment contains general praise, emojis, off-topic content, or spam.
    * **Pending/Needs Manual Review:** Comment intent is ambiguous and difficult to categorize accurately, requiring human intervention.

    ---

    **Please output the result strictly in the following format:**
    **Intent: [Your determined intent category]**
    **Sentiment: [Positive / Neutral / Negative]**
    **Detail Level: [High / Medium / Low]**
    **Geographic Match Score: [0-100]**
    **Scenario Reinforcement: [Based on the comment content, extract all clear pain points, gain points, or specific areas of concern. This information should directly inform sales personnel for personalized outreach. If the comment contains no clear reinforcement points, output "None".]**

    ---

    **Geographic Match Score Calculation Logic**:
    * **100 (Perfect Match):** The user's inquired location (e.g., specific street, district, or city) is explicitly mentioned in the product description as a service area, store address, or the area covered by a discount code.
        * *Example:* User asks "XX Street area", product description lists "123 XX Street" or "XX District service".
    * **70 (Partial Match):** The user's inquired location is a broader area that encompasses a service area or address mentioned in the description, or the description implies regional service without explicit street-level detail that matches the user's inquiry.
        * *Example:* User asks "XX District", product description lists "123 XX Street" (XX Street is within XX District).
    * **30 (Potential Match):** The user asks about a location, but the product description only provides general geographical information (e.g., "nationwide service") or lists areas that are related but not directly matching (e.g., adjacent cities/districts).
        * *Example:* User asks "YY City", product description lists "XX City" (YY City is adjacent to XX City).
    * **0 (No Match):** The user's inquired location is not mentioned or implied in the product description at all, or the product has no geographical relevance.
        * *Example:* User asks "ZZ City", product description makes no mention of ZZ City or surrounding areas.
    * **0 (Not Applicable):** The comment contains no location-specific inquiry, or the product/tool itself has no geographical relevance (e.g., a purely online digital product with no physical presence or service areas).

    ---

    **Scenario Reinforcement Content Examples (for your reference in extraction):**
    * **E-commerce Examples:**
        * **Size:** Inquiry about small size.
        * **Discount:** Asking about promotions/discounts.
        * **Pain Point:** Complaining about XX brand's poor quality/color fading/inaccurate sizing.
        * **Need:** Looking for durable product/store with accurate sizing.
    * **SaaS Examples:**
        * **Feature Need:** Concern about Shopify integration/XX order management system compatibility.
        * **Pain Point:** Complaining about XX tool being complex/hard to use/slow customer support.
        * **Need:** Seeking simpler/more user-friendly tool/better service.

    ---

    **Comment Examples (Few-shot Examples for Model Training):**

    **Example (Own E-commerce Video Comment):**
    Product Description: Our latest spring collection is now available online with nationwide shipping. Enjoy 10% off for new customers on all dresses. Sizes range from XS to XL.
    Comment: Does this dress come in small? I want to buy one, are there any discounts?
    Intent: Purchase Intent
    Sentiment: Positive
    Detail Level: Medium
    Geographic Match Score: 0
    Scenario Reinforcement: Inquiry about small size, asking about discounts

    Product Description: Our Gentle Repair Serum features hypoallergenic, dermatologist-tested ingredients like ceramides and hyaluronic acid, designed for all skin types, especially sensitive skin. Free from parabens, sulfates, and fragrances.
    Comment: My skin is sensitive, will your serum cause allergies? I used XX brand last time and my face got red, are these ingredients safe? Will it irritate?
    Intent: Product/Feature Match Need
    Sentiment: Negative
    Detail Level: High
    Geographic Match Score: 0
    Scenario Reinforcement: Sensitive skin allergy risk, inquiring about ingredient safety, concerns about irritation

    Product Description: Our designer bags are available at our flagship store located at Shanghai, Jing'an District, Huaihai Road 188. Get a special 15% in-store discount this week.
    Comment: Is there a store near Nanjing Road? I'm visiting Shanghai soon and hope to find a discount.
    Intent: Purchase Intent
    Sentiment: Positive
    Detail Level: High
    Geographic Match Score: 70 
    Scenario Reinforcement: Inquiry about store near Nanjing Road, seeking discount

    Product Description: Our summer dresses have a 20% discount at designated stores in Xuhui District, Shanghai, address: No. 233 Hengshan Road.
    Comment: Does this dress come in small? Are there any discounts at the Hengshan Road store?
    Intent: Purchase Intent
    Sentiment: Positive
    Detail Level: Medium
    Geographic Match Score: 100
    Scenario Reinforcement: Inquiry about small size, asking about discounts at Hengshan Road store

    Product Description: Sunshine Gym, located at No. 5 Sanlitun Road, Chaoyang District. Phone: XXXXXXXX. Offers swimming, yoga, equipment training.
    Comment: Do you have a branch near Chaoyang Joy City?
    Intent: Product/Feature Match Need
    Sentiment: Neutral
    Detail Level: High
    Geographic Match Score: 70
    Scenario Reinforcement: Inquiry about branch near Chaoyang Joy City

    **Example (Competitor E-commerce Video Comment):**
    Product Description: This premium leather handbag is crafted from full-grain leather. Available in black and brown.
    Comment: The quality of this bag is so bad, I bought it not long ago and it broke, the stitching came undone. Are there any good alternatives? I hope to find a durable one.
    Intent: Seeking Alternative/Competitor Dissatisfaction
    Sentiment: Negative
    Detail Level: High
    Geographic Match Score: 0
    Scenario Reinforcement: Complaint about XX brand's poor quality (stitching unraveling), seeking durable alternative

    Comment: Their shoe sizes are always off, so frustrating. Looking for a store with accurate sizing.
    Intent: Seeking Alternative/Competitor Dissatisfaction
    Sentiment: Negative
    Detail Level: Medium
    Geographic Match Score: 0
    Scenario Reinforcement: Complaint about XX brand's inaccurate sizing, seeking store with accurate sizing

    **Example (Own SaaS Video Comment):**
    Product Description: Our AI Customer Service system offers seamless integration with Shopify and WooCommerce. Supports over 50 languages and provides 24/7 automated support.
    Comment: Can your AI customer service system integrate with our existing Shopify store? Our order management system is XX, can data flow seamlessly?
    Intent: Product/Feature Match Need
    Sentiment: Positive
    Detail Level: High
    Geographic Match Score: 0
    Scenario Reinforcement: Inquiry about Shopify integration, seamless data flow with XX order management system

    Product Description: Try our Pro productivity tool with a 14-day free trial. No credit card required. Experience features like task management, team collaboration, and reporting.
    Comment: Is there a free trial version? I want to see how it works.
    Intent: Trial/Acquisition Intent
    Sentiment: Neutral
    Detail Level: Medium
    Geographic Match Score: 0
    Scenario Reinforcement: Inquiry about free trial version

    Product Description: Our AI customer service system provides online services nationwide, and has offline consultation points in Beijing and Shanghai. Beijing address: No. 1 Zhongguancun Street, Shanghai address: No. 100 Century Avenue, Pudong New Area.
    Comment: Our company is in Guangzhou, do you have any offline service points there?
    Intent: Product/Feature Match Need
    Sentiment: Neutral
    Detail Level: Medium
    Geographic Match Score: 30
    Scenario Reinforcement: Inquiry about offline service points in Guangzhou

    Product Description: Our cloud-based accounting software offers local support in Singapore, particularly in the Central Business District (CBD). Call us at +65 XXXX XXXX for a consultation.
    Comment: Our office is in Raffles Place. Do you offer on-site training?
    Intent: Product/Feature Match Need
    Sentiment: Positive
    Detail Level: High
    Geographic Match Score: 100
    Scenario Reinforcement: Inquiry about on-site training, located in Raffles Place

    **Example (Competitor SaaS Video Comment):**
    Product Description: Competitor's Product offers Gantt charts, task lists, and reporting features.
    Comment: This project management tool is too complicated, I can't learn it. Too many features, and the interface isn't intuitive. Is there a simpler alternative?
    Intent: Seeking Alternative/Competitor Dissatisfaction
    Sentiment: Negative
    Detail Level: High
    Geographic Match Score: 0
    Scenario Reinforcement: Complaint about XX tool being complex/too many features/unintuitive interface, seeking simpler alternative

    Product Description: Competitor's Product offers 24/7 customer support via chat and email with a guaranteed response time within 15 minutes.
    Comment: Their customer service is too slow, I waited half an hour for an agent and no one responded. Looking for a tool with better service.
    Intent: Seeking Alternative/Competitor Dissatisfaction
    Sentiment: Negative
    Detail Level: High
    Geographic Match Score: 0
    Scenario Reinforcement: Complaint about XX brand's slow customer service/long waiting times, seeking tool with better service

    **Example (General Non-High-Conversion Comment):**
    Comment: Wow, so beautiful! 👍
    Intent: Emotional/Appreciation/Irrelevant
    Sentiment: Positive
    Detail Level: Low
    Geographic Match Score: 0
    Scenario Reinforcement: None

    ---

    **Now, analyze the comment:**

    Comment:
    `
    return instructions;

}


/**
 * Configuration for keyword screening.
 * Adjust these lists based on your specific product/tool and observed comment patterns.
 */
const keywordConfig = {
    // Stage 1: Keywords indicating high conversion potential.
    positiveKeywords: [
        // E-commerce Specific (High Intent)
        'buy', 'want to buy', 'link', 'order', 'how much', 'discount', 'sale', 'promo', 'flash sale',
        'in stock', 'available', 'small size', 'large size', 'color', 'size', 'model', 'delivery', 'free shipping',
        'shipment', 'how to buy', 'where to buy', 'can I buy', 'purchase link', 'coupon', 'promo code',
        // SaaS Specific (High Intent)
        'trial', 'free', 'price', 'paid', 'subscribe', 'register', 'activate', 'integrate', 'API',
        'connect', 'solution', 'solve', 'efficiency', 'need', 'pain point', 'improve', 'effect',
        'trial version', 'pro version', 'premium version',
        // General High Intent (Often Competitor-related)
        'alternative', 'do you have', 'recommend', 'better', 'not as good as', 'complain', 'problem', 'defect', 'not good',
        'too bad', 'slow', 'complex', 'expensive', 'trouble', 'customer service', 'experience'
    ],

    // Stage 2: Keywords indicating low or no commercial value.
    negativeKeywords: [
        'beautiful', 'pretty', 'like', '赞', '👍', '😂', 'hahaha', 'not bad', 'wow',
        'just passing by', 'watching', 'just supporting', 'boring', 'spam', 'emoji', // 'emoji' can be a placeholder if you detect emojis in text
        'expression', 'first comment', 'top' // Common non-value adds
    ],

    // Stage 3: Neutral or ambiguous keywords.
    neutralKeywords: [
        '?', '!', 'what', 'how', 'how about', 'is it', 'is it?',
        'yes', 'can', 'okay', 'product', 'thing', 'this', 'that', 'feature', 'function',
        'real', 'fake', 'is it possible'
    ],

    // Min length for a comment to be considered for negative screening
    minLengthForNegativeScreening: 5
};

/**
 * Performs keyword-based pre-screening of a comment.
 * 
 * @param {string} text - The raw text of the comment to be screened.
 * @returns {string} result - The screening outcome.
 */
function preScreenComment(text) {
    if (!text || typeof text !== 'string') {
        return 'UNKNOWN';
    }

    // Convert comment to lowercase and trim whitespace for consistent matching
    const lowerCaseComment = text.toLowerCase().trim();

    // Stage 1: High-Confidence (Positive Keywords)
    const hasPositiveKeyword = keywordConfig.positiveKeywords.some(
        keyword => lowerCaseComment.includes(keyword)
    );

    if (hasPositiveKeyword) {
        return 'HIGH_CONFIDENCE';
    }

    // Stage 2: Low-Confidence (Negative Keywords)
    const hasNegativeKeyword = keywordConfig.negativeKeywords.some(
        keyword => lowerCaseComment.includes(keyword)
    );

    if (hasNegativeKeyword && lowerCaseComment.length <= keywordConfig.minLengthForNegativeScreening + 10) {
        const isPrimarilyNegative = !lowerCaseComment.split(/\s+/).some(word =>
            !keywordConfig.negativeKeywords.includes(word.toLowerCase()) && word.length > 2
        );
        if (isPrimarilyNegative || lowerCaseComment.trim().length === 0) {
            return 'LOW_VALUE';
        }
    };

    // Stage 3: Neutral or Ambiguous Keywords
    const hasNeutralKeyword = keywordConfig.neutralKeywords.some(
        keyword => lowerCaseComment.includes(keyword.toLowerCase)
    );

    if (hasNeutralKeyword || lowerCaseComment.length > 10) {
        return 'SEND_TO_LLM';
    }

    // If no clear patterns are found, return UNKNOWN
    return 'UNKNOWN';
}


/**
 * 根据评论的意图类别、情绪和详细程度计算意图分数。
 * 该分数旨在量化潜在客户的转化潜力，范围为 0-100。
 *
 * @param {string} intentCategory - 模型的意图分类 (例如: "Purchase Intent", "Seeking Alternative/Competitor Dissatisfaction")。
 * @param {string} sentiment - 评论的情绪 (例如: "Positive", "Neutral", "Negative")。
 * @param {string} detailLevel - 评论的详细程度 (例如: "High", "Medium", "Low")。
 * @returns {number} 计算出的意图分数，范围 [0, 100]。
 */
function calculateIntentScore(intentCategory, sentiment, detailLevel) {
    // 1. 定义意图基础分
    const intentScores = {
        "Purchase Intent": 100,
        "Product/Feature Match Need": 90,
        "Trial/Acquisition Intent": 85,
        "Seeking Alternative/Competitor Dissatisfaction": 95, // 对竞品不满，转化潜力高，包含评论来自竞品视频的信息
        "Specific Size/Spec Inquiry": 98, // 临门一脚的意图
        "General Inquiry/Curiosity": 40,
        "Emotional/Appreciation/Irrelevant": 10,
        "Pending/Needs Manual Review": 0 // 需要人工介入，不自动打分
    };

    // 2. 定义情绪修正分
    const sentimentModifiers = {
        "Positive": 5,
        "Neutral": 0,
        "Negative": -5 // 默认扣分
    };

    // 3. 定义详细程度修正分
    const detailModifiers = {
        "High": 10,
        "Medium": 5,
        "Low": 0
    };

    // 获取基础分，如果意图类别未定义，则默认为 0
    let baseScore = intentScores[intentCategory] || 0;

    // 计算情绪修正分
    let sentimentMod = sentimentModifiers[sentiment] || 0;

    // **特殊处理：对于 "Seeking Alternative/Competitor Dissatisfaction" 意图，
    // 负面情绪反而可能是正面信号（因为是对竞品不满），不应该扣分。**
    if (intentCategory === "Seeking Alternative/Competitor Dissatisfaction" && sentiment === "Negative") {
        sentimentMod = 5; // 或者可以考虑加少量分，例如 +5，表示强烈痛点
    }

    // 计算详细程度修正分
    let detailMod = detailModifiers[detailLevel] || 0;

    // 4. 计算总分
    let totalScore = baseScore + sentimentMod + detailMod;

    // 5. 确保分数在 0 到 100 之间
    let finalScore = Math.max(0, Math.min(100, totalScore));

    return finalScore;
}

/**
 * 计算评论的时效分 (Timeliness Score)
 * 使用指数衰减模型，半衰期为1小时。
 *
 * @param {number} createTime - createTime (Unix 时间戳，秒)。
 * @param {number} currentTimestampMs - 当前时间的 Unix 时间戳 (毫秒)。
 * @param {number} maxScore - 最高时效分，默认为 100。
 * @returns {number} 评论的时效分，范围在 (0, maxScore] 之间，保留两位小数。
 */
function calculateTimeScore(createTime, currentTimestampMs, maxScore = 100) {
    // 1. 获取评论的创建时间（毫秒）
    const createTimeMs = createTime * 1000;

    // 2. 计算时间差 (以小时为单位)
    const timeDifferenceMs = currentTimestampMs - createTimeMs;
    // 如果评论时间在未来 (理论上不应该发生，但为了健壮性) 或非常接近当前时间，直接返回满分
    if (timeDifferenceMs <= 0) {
        return maxScore;
    }
    const timeDifferenceHours = timeDifferenceMs / (1000 * 60 * 60); // 毫秒转换为小时

    // 3. 计算衰减常数 k (半衰期为1小时)
    // k = -ln(0.5)
    const k = -Math.log(0.5); // 约等于 0.693147

    // 4. 应用指数衰减公式
    // TimelinessScore = MaxScore * e^(-k * TimeDifferenceInHours)
    const timelinessScore = maxScore * Math.exp(-k * timeDifferenceHours);

    // 确保分数不会是负数，并保留两位小数
    return parseFloat(Math.max(0, timelinessScore).toFixed(2));
}

/**
 * Main function to perform text analytics on comments.
 * 
 * @param {string} description - product/tool description
 * @param {Comment} comment - comment to analysis
 * @returns {Promise<void>}
 */
export async function commentAnalytics(description, comment, commentTime) {

    if (!description || typeof description !== 'string') {
        throw new Error('description Must be a non-empty string');
    }
    if (!comment.source || typeof comment.source !== 'string') {
        throw new Error('source Must be a non-empty string');
    }
    if (!comment.CommentText || typeof comment.CommentText !== 'string') {
        throw new Error('text Must be a non-empty string');
    }

    const prompt = textPrompt(description, comment.source);

    const result = preScreenComment(comment.CommentText)
    if (result == "HIGH_CONFIDENCE" || "SEND_TO_LLM") {
        const completion = await openAI.chat.completions.create({
            model: "deepseek-reasoner",
            messages: [{
                role: "system",
                content: prompt
            }, {
                role: "user",
                content: `Comment: ${comment.CommentText}`
            }]
        });

        const output = completion.choices[0].message.content;

        const intentMatch = output.match(/\*\*Intent:\s*([^\*]+)\*\*/i) || output.match(/Intent:\s*([^\n]+)/i);
        const intent = intentMatch ? intentMatch[1].trim() : null;

        const sentimentMatch = output.match(/\*\*?Sentiment:\s*([^\*\n]+)\*\*/i) || output.match(/Sentiment:\s*([^\n]+)/i);
        const sentiment = sentimentMatch ? sentimentMatch[1].trim() : null;

        const detailLevelMath = output.match(/\*\*?Detail Level:\s*([^\*\n]+)\*\*/i) || output.match(/Detail Level:\s*([^\n]+)/i);
        const detailLevel = detailLevelMath ? detailLevelMath[1].trim() : null;

        const geographicMatchScoreMatch = output.match(/\*\*?Geographic Match Score:\s*([^\*\n]+)\*\*/i) || output.match(/Geographic Match Score:\s*([^\n]+)/i);
        const geographicMatchScore = geographicMatchScoreMatch ? Number(geographicMatchScoreMatch[1].trim()) : 0; // 地理匹配分

        const scenarioMatch = output.match(/\*\*Scenario Reinforcement:\*\*\s*([\s\S]*?)(\n\s*\*\*|$)/i) || output.match(/Scenario Reinforcement:\s*([\s\S]*?)(\n\S|$)/i);
        const scenarioReinforcement = scenarioMatch ? scenarioMatch[1].trim() : null;

        const intentScore = calculateIntentScore(intent, sentiment, detailLevel, scenarioReinforcement); // 意图强度分

        const currentTimestampMs = Date.now()
        const timeScore = calculateTimeScore(commentTime, currentTimestampMs); // 时效分

        return { result, intentScore, timeScore, geographicMatchScore, scenarioReinforcement } // 分类结果，意图强度分，时效分，地理匹配分，场景强化
    }
}