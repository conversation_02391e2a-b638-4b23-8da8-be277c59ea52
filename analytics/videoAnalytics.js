// Analyze video
import <PERSON><PERSON><PERSON> from "openai";
import dotenv from "dotenv";
import fs from 'fs';

dotenv.config();
const openAI = new OpenAI({
    apiKey: process.env.DEEPSEEK_API_KEY,
    baseURL: "https://api.deepseek.com",
});

function videoPrompt(description) {
    const itemDescription = description || `Our gym is high-energy, community-focused, premium, results-driven, holistic wellness, yoga-centric, powerlifting, strength-focused, casual and beginner-friendly at Nanjin round, Shanghai .`

    const instructions = `
    You are an expert social media analyst specializing in KOC identification for small to medium-sized businesses (SMBs). Your task is to evaluate TikTok creators based on provided video data to determine their suitability as a KOC for a specific business. You will also extract key content points from their videos.
    
    ---

    **[Dynamic Insertion Area: Business Type, KOC Criteria, and Brand Tone/Niche]**

    Here is key information about the businesses we are analyzing:
    ${itemDescription}

    ---

    **Your Analysis Should Include:**

    1.  **Creator Overview & Eligibility:**
        * **TikTok Username:** Extract "authorMeta.name".
        * **Display Name:** Extract "authorMeta.nickName".
        * **Current Fan Count:** Extract "authorMeta.fans".
        * **KOC Eligibility Check:** Based on the provided "KOC Criteria", state whether this creator meets the fan count/follower range requirement. This is a primary filter.

    2.  **Detailed Content Analysis (if creator meets eligibility criteria):**
        * **Video Caption Summary:** Paraphrase the "text" field, highlighting core themes, keywords, and the overall message (e.g., "product review," "daily routine," "skill tutorial," "lifestyle vlog," "motivational content").
        * **Relevant Hashtags:** List all pertinent hashtags from "hashtags.name" that relate to the creator's content and could potentially align with the business.
        * **Creator's Bio/Signature Analysis:** Summarize "authorMeta.signature", noting any explicit interests, specializations, or calls to action (e.g., "link in bio," "DM for collaborations," "focus on fitness and mental health").
        * **Inferred Content Style/Theme:** Based on the above, describe the overarching style, niche, or thematic focus of the creator's content (e.g., "DIY tutorials," "fashion hauls," "cooking recipes," "personal development tips," "pet care advice").

    3.  **Engagement Metrics (for context, if creator meets eligibility criteria):**
        * **Total Views:** "playCount"
        * **Likes:** "diggCount"
        * **Comments:** "commentCount"
        * **Shares:** "shareCount"
        * **Saves:** "collectCount"

    4.  **KOC Suitability Recommendation:**
        * **Overall KOC Recommendation:** [Provide a concise "**Recommended for Collaboration**" or "**Not Recommended for Collaboration**" for this specific KOC program.]
        * **Reasoning:**
            * **Eligibility Check:** [Reiterate if the creator meets the "KOC Criteria" (e.g., "Creator's fan count [X] is within the target range [Y-Z]"). If not eligible, explain why (e.g., "Creator's fan count [X] exceeds the target limit [Z]").]
            * **Content Relevance:** [Briefly explain how the creator's general content and this specific video's focus align or misalign with the "Business Type" and its services/products.]
            * **Brand Tone Match Score (0-100):** [Provide a numerical score (e.g., **85**). **0 indicates no match, 100 indicates a perfect match.**]
            * **Tone Match Justification:** [Explain in detail *why* you assigned that specific score, drawing direct connections between the creator's **inferred content style/theme** and the "Business's Brand Tone/Niche". Provide specific examples from the data to support your assessment.]
            * **Geographic Match Score (0-100):** [Provide a numerical score (e.g., **90**). **0 indicates no geographic relevance, 100 indicates clear local presence.**]
            * **Geographic Match Justification:** [Explain *why* you assigned this score. Reference specific clues from the "Video Caption Summary", "Creator's Bio/Signature Analysis", or any **explicit video location data** (if available in the JSON, though not explicitly in your example) that matches or indicates proximity to the "Business's Operational Location".]
    
    5. **Key Video Content Takeaways**
        * **Main Message/Focus:** [What is the primary message or focus of this specific video?]
        * **Potential Collaboration Angles:** [Based on this video, what specific types of content, products, or services could this creator effectively promote for your business? E.g., "Could promote sustainable pet products," "Ideal for showcasing new coffee brewing techniques," "Can feature detailed product review content."]
        * **Audience Appeal:** [What aspects of this video would likely appeal to your target audience?]
    ---

    **Please output the result strictly in the following format:**
    
    **Brand Tone Match Score: [0-100]**
    **Geographic Match Score: [0-100]**
    **Potential Collaboration: [Your determined Potential Collaboration]**
    
    ---

    **Now, analyze the video:**

    Video:
    `

    return instructions;
}

/**
 * 计算视频的时效分 (Timeliness Score)
 * 使用指数衰减模型，半衰期为24小时。
 *
 * @param {number} createTime - createTime (Unix 时间戳，秒)。
 * @param {number} currentTimestampMs - 当前时间的 Unix 时间戳 (毫秒)。
 * @param {number} maxScore - 最高时效分，默认为 100。
 * @returns {number} 评论的时效分，范围在 (0, maxScore] 之间，保留两位小数。
 */
function calculateTimeScore(createTime, currentTimestampMs, maxScore = 100) {
    // 1. 获取视频的创建时间（毫秒）
    const createTimeMs = createTime * 1000;

    // 2. 计算时间差 (以小时为单位)
    const timeDifferenceMs = currentTimestampMs - createTimeMs;
    // 如果评论时间在未来 (理论上不应该发生，但为了健壮性) 或非常接近当前时间，直接返回满分
    if (timeDifferenceMs <= 0) {
        return maxScore;
    }
    const timeDifferenceHours = timeDifferenceMs / (1000 * 60 * 60); // 毫秒转换为小时

    // 3. 计算衰减常数 k (半衰期为24小时)
    // k = -ln(12)
    const k = -Math.log(12);

    // 4. 应用指数衰减公式
    // TimelinessScore = MaxScore * e^(-k * TimeDifferenceInHours)
    const timelinessScore = maxScore * Math.exp(-k * timeDifferenceHours);

    // 确保分数不会是负数，并保留两位小数
    return parseFloat(Math.max(0, timelinessScore).toFixed(2));
}


/**
 * Main function to perform video analytics.
 * 
 * @param {string} description - KOC description
 * @param {Video} video - video to analysis
 * @returns 
 */
export async function videoAnalytics(description, video) {

    if (!description || typeof description !== 'string') {
        throw new Error('description Must be a non-empty string');
    }
    if (!video.createTime || isNaN(Number(video.createTime))) {
        throw new Error('createTime must be a valid numeric timestamp');
    }

    const prompt = videoPrompt(description);

    const completion = await openAI.chat.completions.create({
        model: "deepseek-reasoner",
        messages: [{
            role: "system",
            content: prompt
        }, {
            role: "user",
            content: `Video: ${JSON.stringify(video)}`
        }]
    });

    const output = completion.choices[0].message.content;

    const brandToneMatchScoreMatch = output.match(/\*\*?Brand Tone Match Score:\s*([^\*\n]+)\*\*/i) || output.match(/Brand Tone Match Score:\s*([^\n]+)/i);
    const brandToneMatchScore = brandToneMatchScoreMatch ? Number(brandToneMatchScoreMatch[1].trim()) : 0; // 品牌匹配分

    const geographicMatchScoreMatch = output.match(/\*\*?Geographic Match Score:\s*([^\*\n]+)\*\*/i) || output.match(/Geographic Match Score:\s*([^\n]+)/i);
    const geographicMatchScore = geographicMatchScoreMatch ? Number(geographicMatchScoreMatch[1].trim()) : 0; // 地理匹配分

    const potentialCollaborationMath = output.match(/\*\*Potential Collaboration:\*\*\s*([\s\S]*?)(\n\s*\*\*|$)/i) || output.match(/Potential Collaboration:\s*([\s\S]*?)(\n\S|$)/i);
    const potentialCollaboration = potentialCollaborationMath ? potentialCollaborationMath[1].trim() : null;

    const currentTimestampMs = Date.now()
    const timeScore = calculateTimeScore(video.createTime, currentTimestampMs); // 时效分

    return { brandToneMatchScore, timeScore, geographicMatchScore, potentialCollaboration } // 品牌匹配分，时效分，地理匹配分，潜在合作点
}