import { GoogleGenAI } from '@google/genai'
import { config } from 'dotenv'

config()

const genAi = new GoogleGenAI({
    apiKey: process.env.GEMINI_API_KEY || "AIzaSyA7_TM7bCZjWg3j3ZZB3_VHb1A_eMY3lNQ"
})

const description = "Our product is an AI-powered e-commerce smart customer service SaaS tool that helps merchants automate customer inquiries and improve conversion rates." // 可拓展，这里可以插入产品/工具的描述信息，便于模型理解评论内容和产品特性。
const source = "Competitor Video" // or "Our Own Video"

async function main() {
    const response = await genAi.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: [{text: "This service is such a bull shit. I can't believe I wasted my time on this. I want some AI-powered stuff!"}], // 经过一级过滤的评论
        config: {
            thinkingConfig: { thinkingBudget: 0 }, // 停止思维链，降低开发消耗。实际生产可以设置为1024，常用
            temperature: 0.3, // 0～0.3，发现更多问题。实际生产自行调整，找到合适温度，复现模型需要该值，属于机密值，。
            maxOutputTokens: 80, // 降低开发消耗。实际生产设置30～180，自行调整。
            stopSequences: ["\n", "Motion:"],
            systemInstruction: `
                You are an expert market analyst, specialized in identifying and extracting high-conversion potential leads. Your task is to analyze user comments on TikTok ad videos, accurately identifying their underlying purchase intent and high-value needs. Crucially, you must extract any exposed user pain points, gain points, or specific areas of concern from the comments, and refine them into "scenario reinforcement" information that sales personnel can use to craft personalized outreach. Additionally, you need to calculate a "Geographic Match Score" based on any location-specific inquiries in the comment and the geographical information available in the product description.

                ---

                **[Dynamic Insertion Area: Product/Tool Description]**
                Here is key information about the product/tool we are analyzing:
                ${description}
                **(Note: This description may contain geographical information such as service areas, store addresses, or location-based discount codes.)**

                ---

                **[Dynamic Insertion Area: Comment Source Specific Instructions]**
                Here is the source of the comment you are analyzing :
                ${source}

                **If the comment is from "Our Own Video":**
                Please focus on identifying explicit purchase intent, specific inquiries, or clear needs related to our product.

                **If the comment is from "Competitor Video":**
                Please prioritize identifying user dissatisfaction with the competitor, demands for missing features, or inquiries about alternative solutions. These are all considered high-conversion potential for us.

                ---

                **Intent Categories (Choose strictly one as the result):**

                **High-Conversion Potential Intents:**
                * **Purchase Intent:** User explicitly expresses desire to buy, asks about purchasing methods, payment, price (if own video comment). Or expresses dissatisfaction with competitor pricing and seeks alternatives (if competitor video comment).
                * **Product/Feature Match Need:** User describes a specific need or pain point and asks if the product/tool offers a specific solution or contains a core feature/characteristic.
                * **Trial/Acquisition Intent:** User asks how to trial, about free versions, downloads, registration, or how to get the product/tool (common for SaaS).
                * **Seeking Alternative/Competitor Dissatisfaction (Competitor Comments ONLY):** User expresses strong negative sentiment towards a competitor, complains about its flaws, or explicitly asks for better alternatives.
                * **Specific Size/Spec Inquiry (E-commerce ONLY):** User asks about the availability of a specific size, color, model, or style, typically a final confirmation before purchase.

                **Other Intents (Not high-conversion potential, but still identified):**
                * **General Inquiry/Curiosity:** User asks general questions or expresses curiosity, but without clear purchase or high-value intent.
                * **Emotional/Appreciation/Irrelevant:** Comment contains general praise, emojis, off-topic content, or spam.
                * **Pending/Needs Manual Review:** Comment intent is ambiguous and difficult to categorize accurately, requiring human intervention.

                ---

                **Please output the result strictly in the following format:**
                **Intent: [Your determined intent category]**
                **Sentiment: [Positive / Neutral / Negative]**
                **Detail Level: [High / Medium / Low]**
                **Geographic Match Score: [0-100]**
                **Scenario Reinforcement: [Based on the comment content, extract all clear pain points, gain points, or specific areas of concern. This information should directly inform sales personnel for personalized outreach. If the comment contains no clear reinforcement points, output "None".]**

                ---

                **Geographic Match Score Calculation Logic**:
                * **100 (Perfect Match):** The user's inquired location (e.g., specific street, district, or city) is explicitly mentioned in the product description as a service area, store address, or the area covered by a discount code.
                    * *Example:* User asks "XX Street area", product description lists "123 XX Street" or "XX District service".
                * **70 (Partial Match):** The user's inquired location is a broader area that encompasses a service area or address mentioned in the description, or the description implies regional service without explicit street-level detail that matches the user's inquiry.
                    * *Example:* User asks "XX District", product description lists "123 XX Street" (XX Street is within XX District).
                * **30 (Potential Match):** The user asks about a location, but the product description only provides general geographical information (e.g., "nationwide service") or lists areas that are related but not directly matching (e.g., adjacent cities/districts).
                    * *Example:* User asks "YY City", product description lists "XX City" (YY City is adjacent to XX City).
                * **0 (No Match):** The user's inquired location is not mentioned or implied in the product description at all, or the product has no geographical relevance.
                    * *Example:* User asks "ZZ City", product description makes no mention of ZZ City or surrounding areas.
                * **0 (Not Applicable):** The comment contains no location-specific inquiry, or the product/tool itself has no geographical relevance (e.g., a purely online digital product with no physical presence or service areas).

                ---

                **Scenario Reinforcement Content Examples (for your reference in extraction):**
                * **E-commerce Examples:**
                    * **Size:** Inquiry about small size.
                    * **Discount:** Asking about promotions/discounts.
                    * **Pain Point:** Complaining about XX brand's poor quality/color fading/inaccurate sizing.
                    * **Need:** Looking for durable product/store with accurate sizing.
                * **SaaS Examples:**
                    * **Feature Need:** Concern about Shopify integration/XX order management system compatibility.
                    * **Pain Point:** Complaining about XX tool being complex/hard to use/slow customer support.
                    * **Need:** Seeking simpler/more user-friendly tool/better service.

                ---

                **Comment Examples (Few-shot Examples for Model Training):**

                **Example (Own E-commerce Video Comment):**
                Product Description: Our latest spring collection is now available online with nationwide shipping. Enjoy 10% off for new customers on all dresses. Sizes range from XS to XL.
                Comment: Does this dress come in small? I want to buy one, are there any discounts?
                Intent: Purchase Intent
                Sentiment: Positive
                Detail Level: Medium
                Geographic Match Score: 0
                Scenario Reinforcement: Inquiry about small size, asking about discounts

                Product Description: Our Gentle Repair Serum features hypoallergenic, dermatologist-tested ingredients like ceramides and hyaluronic acid, designed for all skin types, especially sensitive skin. Free from parabens, sulfates, and fragrances.
                Comment: My skin is sensitive, will your serum cause allergies? I used XX brand last time and my face got red, are these ingredients safe? Will it irritate?
                Intent: Product/Feature Match Need
                Sentiment: Negative
                Detail Level: High
                Geographic Match Score: 0
                Scenario Reinforcement: Sensitive skin allergy risk, inquiring about ingredient safety, concerns about irritation

                Product Description: Our designer bags are available at our flagship store located at Shanghai, Jing'an District, Huaihai Road 188. Get a special 15% in-store discount this week.
                Comment: Is there a store near Nanjing Road? I'm visiting Shanghai soon and hope to find a discount.
                Intent: Purchase Intent
                Sentiment: Positive
                Detail Level: High
                Geographic Match Score: 70 
                Scenario Reinforcement: Inquiry about store near Nanjing Road, seeking discount

                Product Description: Our summer dresses have a 20% discount at designated stores in Xuhui District, Shanghai, address: No. 233 Hengshan Road.
                Comment: Does this dress come in small? Are there any discounts at the Hengshan Road store?
                Intent: Purchase Intent
                Sentiment: Positive
                Detail Level: Medium
                Geographic Match Score: 100
                Scenario Reinforcement: Inquiry about small size, asking about discounts at Hengshan Road store

                Product Description: Sunshine Gym, located at No. 5 Sanlitun Road, Chaoyang District. Phone: XXXXXXXX. Offers swimming, yoga, equipment training.
                Comment: Do you have a branch near Chaoyang Joy City?
                Intent: Product/Feature Match Need
                Sentiment: Neutral
                Detail Level: High
                Geographic Match Score: 70
                Scenario Reinforcement: Inquiry about branch near Chaoyang Joy City

                **Example (Competitor E-commerce Video Comment):**
                Product Description: This premium leather handbag is crafted from full-grain leather. Available in black and brown.
                Comment: The quality of this bag is so bad, I bought it not long ago and it broke, the stitching came undone. Are there any good alternatives? I hope to find a durable one.
                Intent: Seeking Alternative/Competitor Dissatisfaction
                Sentiment: Negative
                Detail Level: High
                Geographic Match Score: 0
                Scenario Reinforcement: Complaint about XX brand's poor quality (stitching unraveling), seeking durable alternative

                Comment: Their shoe sizes are always off, so frustrating. Looking for a store with accurate sizing.
                Intent: Seeking Alternative/Competitor Dissatisfaction
                Sentiment: Negative
                Detail Level: Medium
                Geographic Match Score: 0
                Scenario Reinforcement: Complaint about XX brand's inaccurate sizing, seeking store with accurate sizing

                **Example (Own SaaS Video Comment):**
                Product Description: Our AI Customer Service system offers seamless integration with Shopify and WooCommerce. Supports over 50 languages and provides 24/7 automated support.
                Comment: Can your AI customer service system integrate with our existing Shopify store? Our order management system is XX, can data flow seamlessly?
                Intent: Product/Feature Match Need
                Sentiment: Positive
                Detail Level: High
                Geographic Match Score: 0
                Scenario Reinforcement: Inquiry about Shopify integration, seamless data flow with XX order management system

                Product Description: Try our Pro productivity tool with a 14-day free trial. No credit card required. Experience features like task management, team collaboration, and reporting.
                Comment: Is there a free trial version? I want to see how it works.
                Intent: Trial/Acquisition Intent
                Sentiment: Neutral
                Detail Level: Medium
                Geographic Match Score: 0
                Scenario Reinforcement: Inquiry about free trial version

                Product Description: Our AI customer service system provides online services nationwide, and has offline consultation points in Beijing and Shanghai. Beijing address: No. 1 Zhongguancun Street, Shanghai address: No. 100 Century Avenue, Pudong New Area.
                Comment: Our company is in Guangzhou, do you have any offline service points there?
                Intent: Product/Feature Match Need
                Sentiment: Neutral
                Detail Level: Medium
                Geographic Match Score: 30
                Scenario Reinforcement: Inquiry about offline service points in Guangzhou

                Product Description: Our cloud-based accounting software offers local support in Singapore, particularly in the Central Business District (CBD). Call us at +65 XXXX XXXX for a consultation.
                Comment: Our office is in Raffles Place. Do you offer on-site training?
                Intent: Product/Feature Match Need
                Sentiment: Positive
                Detail Level: High
                Geographic Match Score: 100
                Scenario Reinforcement: Inquiry about on-site training, located in Raffles Place

                **Example (Competitor SaaS Video Comment):**
                Product Description: Competitor's Product offers Gantt charts, task lists, and reporting features.
                Comment: This project management tool is too complicated, I can't learn it. Too many features, and the interface isn't intuitive. Is there a simpler alternative?
                Intent: Seeking Alternative/Competitor Dissatisfaction
                Sentiment: Negative
                Detail Level: High
                Geographic Match Score: 0
                Scenario Reinforcement: Complaint about XX tool being complex/too many features/unintuitive interface, seeking simpler alternative

                Product Description: Competitor's Product offers 24/7 customer support via chat and email with a guaranteed response time within 15 minutes.
                Comment: Their customer service is too slow, I waited half an hour for an agent and no one responded. Looking for a tool with better service.
                Intent: Seeking Alternative/Competitor Dissatisfaction
                Sentiment: Negative
                Detail Level: High
                Geographic Match Score: 0
                Scenario Reinforcement: Complaint about XX brand's slow customer service/long waiting times, seeking tool with better service

                **Example (General Non-High-Conversion Comment):**
                Comment: Wow, so beautiful! 👍
                Intent: Emotional/Appreciation/Irrelevant
                Sentiment: Positive
                Detail Level: Low
                Geographic Match Score: 0
                Scenario Reinforcement: None

                ---

                **Now, analyze the comment:**

                Comment:
            `,
        }
    })
    console.log(response.data)
}

main()