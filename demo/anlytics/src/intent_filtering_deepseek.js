import OpenAI from "openai";
import dotenv from "dotenv";
dotenv.config();

const openAI = new OpenAI({
    apiKey: "sk-2a790fb120ec4f2e8f38eac03b7eed9e",
    baseURL: "https://api.deepseek.com",
});
/**
 * Construct prompt
 * @param description - tools/products description
 * 
 */
async function constructPrompt() {
    const description = `Our product is an AI-powered e-commerce smart customer service SaaS tool that helps merchants automate customer inquiries and improve conversion rates.`;
    const commentSource = `Competitor Video`; // or "Our Own Video"
    const intentCategories = `
**High-Conversion Potential Intents:**
* **Purchase Intent:** User explicitly expresses desire to buy, asks about purchasing methods, payment, price (if own video comment). Or expresses dissatisfaction with competitor pricing and seeks alternatives (if competitor video comment).
* **Product/Feature Match Need:** User describes a specific need or pain point and asks if the product/tool offers a specific solution or contains a core feature/characteristic.
* **Trial/Acquisition Intent:** User asks how to trial, about free versions, downloads, registration, or how to get the product/tool (common for SaaS).
* **Seeking Alternative/Competitor Dissatisfaction (Competitor Comments ONLY):** User expresses strong negative sentiment towards a competitor, complains about its flaws, or explicitly asks for better alternatives.
* **Specific Size/Spec Inquiry (E-commerce ONLY):** User asks about the availability of a specific size, color, model, or style, typically a final confirmation before purchase.

**Other Intents (Not high-conversion potential, but still identified):**
* **General Inquiry/Curiosity:** User asks general questions or expresses curiosity, but without clear purchase or high-value intent.
* **Emotional/Appreciation/Irrelevant:** Comment contains general praise, emojis, off-topic content, or spam.
* **Pending/Needs Manual Review:** Comment intent is ambiguous and difficult to categorize accurately, requiring human intervention.
`; // 用户不可改。影响后续意图强度计算，开发人员若需要添加新的意图类别，可以在这里修改，注意修改意图强度计算。
    const commentExamples = null; // 用户可改，应增加审核机制，影响模型特征空间，低质样例降低意图计算准确性。

    const instructions = `
You are an expert market analyst, specialized in identifying and extracting high-conversion potential leads. Your task is to analyze user comments on TikTok ad videos, accurately identifying their underlying purchase intent and high-value needs. Crucially, you must extract any exposed user pain points, gain points, or specific areas of concern from the comments, and refine them into "scenario reinforcement" information that sales personnel can use to craft personalized outreach.

---

**[Dynamic Insertion Area: Product/Tool Description]**
Here is key information about the product/tool we are analyzing:
${description}

---

**[Dynamic Insertion Area: Comment Source Specific Instructions]**
Here is the source of the comment you are analyzing :
${commentSource}

**If the comment is from "Our Own Video":**
Please focus on identifying explicit purchase intent, specific inquiries, or clear needs related to our product.

**If the comment is from "Competitor Video":**
Please prioritize identifying user dissatisfaction with the competitor, demands for missing features, or inquiries about alternative solutions. These are all considered high-conversion potential for us.

---

**Intent Categories (Choose strictly one as the result):**

${intentCategories}

---

**Please output the result strictly in the following format:**
**Intent: [Your determined intent category]**
**Sentiment: [Positive / Neutral / Negative]**
**Detail Level: [High / Medium / Low]**
**Scenario Reinforcement: [Based on the comment content, extract all clear pain points, gain points, or specific areas of concern. This information should directly inform sales personnel for personalized outreach. If the comment contains no clear reinforcement points, output "None".]**

---

**Scenario Reinforcement Content Examples (for your reference in extraction):**
* **E-commerce Examples:**
    * **Size:** Inquiry about small size.
    * **Discount:** Asking about promotions/discounts.
    * **Pain Point:** Complaining about XX brand's poor quality/color fading/inaccurate sizing.
    * **Need:** Looking for durable product/store with accurate sizing.
* **SaaS Examples:**
    * **Feature Need:** Concern about Shopify integration/XX order management system compatibility.
    * **Pain Point:** Complaining about XX tool being complex/hard to use/slow customer support.
    * **Need:** Seeking simpler/more user-friendly tool/better service.

---

**Comment Examples (Few-shot Examples for Model Training):**

**Example (Own E-commerce Video Comment):**
Comment: Does this dress come in small? I want to buy one, are there any discounts?
Intent: Purchase Intent
Sentiment: Positive
Detail Level: Medium
Scenario Reinforcement: Inquiry about small size, asking about discounts

Comment: My skin is sensitive, will your serum cause allergies? I used XX brand last time and my face got red, are these ingredients safe? Will it irritate?
Intent: Product/Feature Match Need
Sentiment: Negative
Detail Level: High
Scenario Reinforcement: Sensitive skin allergy risk, inquiring about ingredient safety, concerns about irritation

**Example (Competitor E-commerce Video Comment):**
Comment: The quality of this bag is so bad, I bought it not long ago and it broke, the stitching came undone. Are there any good alternatives? I hope to find a durable one.
Intent: Seeking Alternative/Competitor Dissatisfaction
Sentiment: Negative
Detail Level: High
Scenario Reinforcement: Complaint about XX brand's poor quality (stitching unraveling), seeking durable alternative

Comment: Their shoe sizes are always off, so frustrating. Looking for a store with accurate sizing.
Intent: Seeking Alternative/Competitor Dissatisfaction
Sentiment: Negative
Detail Level: Medium
Scenario Reinforcement: Complaint about XX brand's inaccurate sizing, seeking store with accurate sizing

**Example (Own SaaS Video Comment):**
Comment: Can your AI customer service system integrate with our existing Shopify store? Our order management system is XX, can data flow seamlessly?
Intent: Product/Feature Match Need
Sentiment: Positive
Detail Level: High
Scenario Reinforcement: Inquiry about Shopify integration, seamless data flow with XX order management system

Comment: Is there a free trial version? I want to see how it works.
Intent: Trial/Acquisition Intent
Sentiment: Neutral
Detail Level: Medium
Scenario Reinforcement: Inquiry about free trial version

**Example (Competitor SaaS Video Comment):**
Comment: This project management tool is too complicated, I can't learn it. Too many features, and the interface isn't intuitive. Is there a simpler alternative?
Intent: Seeking Alternative/Competitor Dissatisfaction
Sentiment: Negative
Detail Level: High
Scenario Reinforcement: Complaint about XX tool being complex/too many features/unintuitive interface, seeking simpler alternative

Comment: Their customer service is too slow, I waited half an hour for an agent and no one responded. Looking for a tool with better service.
Intent: Seeking Alternative/Competitor Dissatisfaction
Sentiment: Negative
Detail Level: High
Scenario Reinforcement: Complaint about XX brand's slow customer service/long waiting times, seeking tool with better service

**Example (General Non-High-Conversion Comment):**
Comment: Wow, so beautiful! 👍
Intent: Emotional/Appreciation/Irrelevant
Sentiment: Positive
Detail Level: Low
Scenario Reinforcement: None

${commentExamples ? commentExamples : ``}

---

**Now, analyze the comment:**

Comment:
`
return instructions;

}

// async function updateCommentSummary() {}

// async function summarizeText() {}

// async function getCommentByCid() {}

// /**
//  * Format the comment for analysis
//  * @param comment - the comment to be formatted
//  */
// async function formatComment(cid) {
//     let summaries = [];
//     let currentCid = cid;

//     let comment = await getCommentByCid(currentCid); // getCommentByCid未实现，需要根据数据库结构实现
//     while (comment.repliesToId) {
//         const parentComment = await getCommentByCid(comment.repliesToId);

//         if (!parentComment.textSummary) {
//             const summary = await summarizeText(parentComment.text); // summarizeText未实现，调用llm实现
//             parentComment.textSummary = summary;
//             await updateCommentSummary(parentComment.cid, summary) // updateCommentSummary未实现，需要根据数据库结构实现
//         }
//         summaries.unshift(parentComment.textSummary);
//         comment = parentComment;
//     }

//     const originalComment = await getCommentById(cid);

//     return [...summaries, originalComment.text].join(',');
// }

async function main() {
    // 测试用例，生产场景中由爬虫爬取评论内容
    const comment = "This service is such a bull shit. I can't believe I wasted my time on this. I want some AI-powered stuff!"; // 这里可以插入需要分析的评论内容
    // const comment = await formatComment(cid) // cid 为待分析评论的id

    const completion = await openAI.chat.completions.create({
        model: "deepseek-reasoner",
        messages: [{
            role: "system",
            content: await constructPrompt()
        }, {
            role: "user",
            content: `Comment: ${comment}`
        }]
    });
    const output = completion.choices[0].message.content;
    console.log(output);

    const intentMatch = output.match(/\*\*Intent:\s*([^\*]+)\*\*/i) || output.match(/Intent:\s*([^\n]+)/i);
    const intent = intentMatch ? intentMatch[1].trim() : null;

    const scenarioMatch = output.match(/\*\*Scenario Reinforcement:\*\*\s*([\s\S]*?)(\n\s*\*\*|$)/i) || output.match(/Scenario Reinforcement:\s*([\s\S]*?)(\n\S|$)/i);
    const scenarioReinforcement = scenarioMatch ? scenarioMatch[1].trim() : null;

    console.log(`Intent:`, intent);
    console.log(`Scenario Reinforcement:`,scenarioReinforcement);
}

main();