import os
import hashlib
import pymongo
from apify_client import ApifyClient
from datetime import datetime
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置
APIFY_TOKEN = os.getenv("APIFY_TOKEN")
MONGO_URI = os.getenv("MONGO_URI", "mongodb://localhost:27017/")

# MongoDB 配置
mongo_client = pymongo.MongoClient(MONGO_URI)
db = mongo_client["tiktok_data"]
comments_collection = db["tiktok_comments"]
comments_collection.create_index("created_at", expireAfterSeconds=7*24*60*60)

# Apify 客户端
apify_client = ApifyClient(APIFY_TOKEN)


# SHA-256 哈希隐私信息
def hash_sensitive_data(data):
    return hashlib.sha256(data.encode()).hexdigest()


# 调用 Apify TikTok Comments Scraper
def scrape_tiktok_comments(video_urls, max_items=100):
    actor_input = {
        "postURLs": video_urls,  # 视频 URL 列表
        "commentsPerPost": max_items,  # 限制前 100 条评论
        "maxRepliesPerComment": 0,  # 最大每条评论的回复数
        "resultsPerPage": 100,  # 每页结果数
        "proxySettings": {"useApifyProxy": True},  # 使用 Apify 内置代理
        "maxConcurrency": 1,  # 控制并发
        "maxRequestsPerMinute": 5,  # 限制请求速率
    }

    try:
        # 启动 Actor
        run = apify_client.actor("clockworks/tiktok-comments-scraper").call(
            run_input=actor_input
        )
        if not run:
            print("Apify Actor 调用失败")
            return []

        # 获取结果
        comments = []

        for item in apify_client.dataset(run["defaultDatasetId"]).iterate_items():
            comment = {
                "video_url": item.get("videoWebUrl", ""),
                "comment_id": item.get("cid", ""),
                "text": item.get("text", ""),
                "user_id": hash_sensitive_data(item.get("uid", "")),
                "username": hash_sensitive_data(item.get("uniqueId", "")),
                "timestamp": item.get("createTime", ""),
                "create_time_iso": item.get("createTimeISO", ""),
                "likes": item.get("diggCount", 0),
                "replies_count": item.get("replyCommentTotal", 0),
                "liked_by_author": item.get("likedByAuthor", False),
                "pinned_by_author": item.get("pinnedByAuthor", False),
                "replies_to_id": item.get("repliesToId", None),
                "avatar_thumbnail": item.get("avatarThumbnail", ""),
                "mentions": item.get("mentions", []),
                "replies": [
                    {
                        "reply_id": reply.get("cid", ""),
                        "text": reply.get("text", ""),
                        "user_id": hash_sensitive_data(reply.get("uid", "")),
                        "username": hash_sensitive_data(reply.get("uniqueId", "")),
                        "timestamp": reply.get("createTime", ""),
                        "create_time_iso": reply.get("createTimeISO", ""),
                        "likes": reply.get("diggCount", 0),
                        "liked_by_author": reply.get("likedByAuthor", False),
                        "pinned_by_author": reply.get("pinnedByAuthor", False),
                        "replies_to_id": reply.get("repliesToId", None),
                        "avatar_thumbnail": reply.get("avatarThumbnail", "")
                    } for reply in item.get("replies", [])
                ],
                "created_at": datetime.utcnow()
            }
            comments.append(comment)

        return comments

    except Exception as e:
        print(f"爬取评论失败: {e}")
        return []


# 存储到 MongoDB
def store_comments(comments):
    try:
        if comments:
            comments_collection.insert_many(comments)
            print(f"成功存储 {len(comments)} 条评论到 MongoDB")
        else:
            print("无评论数据存储")
    except Exception as e:
        print(f"存储评论失败: {e}")


# Demo 主函数
def main():
    video_urls = [
        "https://www.tiktok.com/@shinsauce/video/7509602779850083614",
        "https://www.tiktok.com/@christianarcher1.0/video/7519632025381293343",
    ]

    # 爬取评论
    comments = scrape_tiktok_comments(video_urls, max_items=100)

    # 存储到 MongoDB
    store_comments(comments)


if __name__ == "__main__":
    main()
