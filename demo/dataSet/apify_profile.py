import os
import hashlib
import pymongo
from apify_client import Apify<PERSON><PERSON>
from datetime import datetime
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置
APIFY_TOKEN = os.getenv("APIFY_TOKEN", "YOUR_APIFY_TOKEN")
MONGO_URI = os.getenv("MONGO_URI", "mongodb://localhost:27017/")

# MongoDB 配置
try:
    mongo_client = pymongo.MongoClient(MONGO_URI, serverSelectionTimeoutMS=5000)
    mongo_client.server_info()  # 测试连接
    db = mongo_client["tiktok_data"]
    profiles_collection = db["tiktok_profiles"]
    profiles_collection.create_index("created_at", expireAfterSeconds=7 * 24 * 60 * 60)
    print("MongoDB 连接成功")
except Exception as e:
    print(f"MongoDB 连接失败: {e}")
    exit(1)

# Apify 客户端
apify_client = ApifyClient(APIFY_TOKEN)


# SHA-256 哈希隐私信息
def hash_sensitive_data(data):
    return hashlib.sha256(data.encode()).hexdigest() if data else ""


# 调用 Apify TikTok Profile Scraper
def scrape_tiktok_profiles(usernames):
    actor_input = {
        "profiles": usernames,  # 用户名列表（如 ["uniqueId1", "uniqueId2"]）
        "proxySettings": {"useApifyProxy": True},  # 使用 Apify 内置代理
        "maxConcurrency": 1,  # 控制并发
        "maxRequestsPerMinute": 5,  # 限制请求速率
        "maxRequestRetries": 3,  # 重试 3 次
        "timeoutSecs": 30,  # 请求超时 30 秒
    }

    try:
        print(f"启动 Apify Actor, 输入 {len(usernames)} 个用户名")
        run = apify_client.actor("clockworks/tiktok-profile-scraper").call(
            run_input=actor_input
        )
        if not run or run["status"] != "SUCCEEDED":
            print(f"Apify Actor 运行失败: {run.get('status', '未知状态')}")
            return []

        profiles = []
        dataset = apify_client.dataset(run["defaultDatasetId"]).iterate_items()

        for item in dataset:
            if "authorMeta" not in item or not item["authorMeta"].get("id"):
                print(f"用户名 {item.get('input', '未知')} 缺少有效数据，跳过")
                continue

            # 检查 bio 和 bio_link 是否含敏感信息
            signature = item["authorMeta"].get("signature", "")
            bio_link = item["authorMeta"].get("bioLink", None)
            if signature and (
                "@" in signature
                or "http" in signature
                or any(c in signature for c in ["@", ".", "+"])
            ):
                signature = hash_sensitive_data(signature)
            if bio_link and ("http" in str(bio_link)):
                bio_link = hash_sensitive_data(str(bio_link))

            profile = {
                "user_id": hash_sensitive_data(item["authorMeta"].get("id", "")),
                "username": hash_sensitive_data(item["authorMeta"].get("name", "")),
                "nickname": item["authorMeta"].get("nickName", ""),
                "follower_count": item["authorMeta"].get("fans", 0),
                "following_count": item["authorMeta"].get("following", 0),
                "video_count": item["authorMeta"].get("video", 0),
                "bio": signature,
                "avatar_url": item["authorMeta"].get("avatar", ""),
                "verified": item["authorMeta"].get("verified", False),
                "region": item["authorMeta"].get("region", ""),
                "heart": item["authorMeta"].get("heart", 0),
                "private_account": item["authorMeta"].get("privateAccount", False),
                "tt_seller": item["authorMeta"].get("ttSeller", False),
                "bio_link": bio_link,
                "hashtags": [h["name"] for h in item.get("hashtags", [])],
                "video_id": item.get("id", ""),
                "created_at": datetime.utcnow(),
            }
            profiles.append(profile)

        print(f"爬取完成，共 {len(profiles)} 个账号画像")
        for username in usernames:
            profile_count = sum(
                1
                for p in profiles
                if p["username"] == hash_sensitive_data(username.lstrip("@"))
            )
            print(f"用户名 {username}: {'已爬取' if profile_count > 0 else '未爬取'}")

        return profiles

    except Exception as e:
        print(f"爬取账号画像失败: {e}")
        return []


# 存储到 MongoDB
def store_profiles(profiles):
    try:
        if profiles:
            profiles_collection.insert_many(profiles)
            print(f"成功存储 {len(profiles)} 个账号画像到 MongoDB")
        else:
            print("无账号画像数据存储")
    except Exception as e:
        print(f"存储账号画像失败: {e}")


# Demo 主函数
def main():
    # 示例用户名（替换为实际 TikTok 用户名）
    usernames = ["christianarcher1.0", "shinsauce"]

    # 爬取账号画像
    profiles = scrape_tiktok_profiles(usernames, max_items=10)

    # 存储到 MongoDB
    store_profiles(profiles)


if __name__ == "__main__":
    main()
