import os
import requests
import json
import time
from flask import Flask, request, redirect, jsonify
from pymongo import MongoClient
from bson.objectid import ObjectId
from datetime import datetime, timedelta

# 配置信息
app = Flask(__name__)

# 从环境变量获取配置，提高安全性
CLIENT_ID = os.environ.get("TIKTOK_CLIENT_ID")
CLIENT_SECRET = os.environ.get("TIKTOK_CLIENT_SECRET")

ADVERTISER_REDIRECT_URI = os.environ.get(
    "ADVERTISER_REDIRECT_URI", "http://localhost:5000/callback/advertiser"
)
TIKTOK_REDIRECT_URI = os.environ.get(
    "TIKTOK_REDIRECT_URI", "http://localhost:5000/callback/tiktok"
)

MONGODB_URI = os.environ.get("MONGODB_URI", "mongodb://localhost:27017")
DB_NAME = os.environ.get("DB_NAME", "tiktok_auth")

# 授权类型枚举
AUTH_TYPE_ADVERTISER = "advertiser"
AUTH_TYPE_TIKTOK = "tiktok"

# 连接MongoDB
client = MongoClient(MONGODB_URI)
db = client[DB_NAME]
tokens_collection = db["tokens"]


# 工具函数：生成随机状态码，防止CSRF攻击
def generate_state():
    import secrets

    return secrets.token_urlsafe(16)


# 工具函数：保存令牌到数据库
def save_token(auth_type, token_data):
    # 添加时间戳和过期时间计算
    now = datetime.now()
    token_data["created_at"] = now

    # 计算过期时间
    if "expires_in" in token_data:
        token_data["expires_at"] = now + timedelta(seconds=token_data["expires_in"])

    # 如果有刷新令牌，也计算刷新令牌的过期时间
    if "refresh_token_expires_in" in token_data:
        token_data["refresh_token_expires_at"] = now + timedelta(
            seconds=token_data["refresh_token_expires_in"]
        )

    # 保存到数据库
    result = tokens_collection.insert_one(
        {"auth_type": auth_type, "token_data": token_data}
    )

    return result.inserted_id


# 路由：选择授权类型，'type' in ['advertiser', 'tiktok']
@app.route("/authorize", methods=["GET"])
def authorize():
    auth_type = request.args.get("type")
    if auth_type not in [AUTH_TYPE_ADVERTISER, AUTH_TYPE_TIKTOK]:
        return jsonify({"error": "Invalid auth type"}), 400

    # 生成并保存随机状态码
    state = generate_state()

    # 根据授权类型生成不同的授权URL
    if auth_type == AUTH_TYPE_ADVERTISER:
        auth_url = (
            f"https://ads.tiktok.com/marketing_api/auth?"
            f"client_key={CLIENT_ID}"
            f"&redirect_uri={ADVERTISER_REDIRECT_URI}"
            f"&state={state}"
            f"&scope=adgroup_read,report_read"
        )
    else:  # AUTH_TYPE_TIKTOK
        auth_url = (
            f"https://www.tiktok.com/auth/authorize?"
            f"client_key={CLIENT_ID}"
            f"&redirect_uri={TIKTOK_REDIRECT_URI}"
            f"&response_type=code"
            f"&state={state}"
            f"&scope=user.info.basic,video.list"
        )

    # 保存状态码与授权类型的映射（实际应用中可能需要更安全的存储方式）
    # 这里简化处理，实际应该使用数据库或缓存存储
    return redirect(auth_url)


# 路由：广告主授权回调
@app.route("/authorize/callback/advertiser", methods=["GET"])
def advertiser_callback():
    # 获取授权码和状态
    code = request.args.get("code")
    state = request.args.get("state")
    error = request.args.get("error")

    if error:
        return (
            jsonify(
                {
                    "error": error,
                    "error_description": request.args.get("error_description"),
                }
            ),
            400,
        )

    if not code:
        return jsonify({"error": "Authorization code not provided"}), 400

    # 验证状态码（实际应用中应与之前保存的状态码比较）

    # 换取访问令牌
    token_data = exchange_advertiser_token(code)
    if not token_data:
        return jsonify({"error": "Failed to exchange access token"}), 500

    # 保存令牌到数据库
    token_id = save_token(AUTH_TYPE_ADVERTISER, token_data)

    return jsonify(
        {
            "message": "Authorization successful",
            "token_id": str(token_id),
            "token_summary": {
                "access_token": token_data.get("access_token")[:10]
                + "...",  # 只显示部分令牌
                "expires_in": token_data.get("expires_in"),
                "scope": token_data.get("scope"),
            },
        }
    )


# 路由：TikTok原生账号授权回调
@app.route("/authorize/callback/tiktok", methods=["GET"])
def tiktok_callback():
    # 获取授权码和状态
    code = request.args.get("code")
    state = request.args.get("state")
    error = request.args.get("error")

    if error:
        return (
            jsonify(
                {
                    "error": error,
                    "error_description": request.args.get("error_description"),
                }
            ),
            400,
        )

    if not code:
        return jsonify({"error": "Authorization code not provided"}), 400

    # 验证状态码（实际应用中应与之前保存的状态码比较）

    # 换取访问令牌
    token_data = exchange_tiktok_token(code)
    if not token_data:
        return jsonify({"error": "Failed to exchange access token"}), 500

    # 保存令牌到数据库
    token_id = save_token(AUTH_TYPE_TIKTOK, token_data)

    return jsonify(
        {
            "message": "Authorization successful",
            "token_id": str(token_id),
            "token_summary": {
                "access_token": token_data.get("access_token")[:10]
                + "...",  # 只显示部分令牌
                "expires_in": token_data.get("expires_in"),
                "refresh_token": (
                    token_data.get("refresh_token")[:10] + "..."
                    if "refresh_token" in token_data
                    else None
                ),
                "refresh_expires_in": token_data.get("refresh_expires_in"),
            },
        }
    )


# 函数：换取广告主访问令牌
def exchange_advertiser_token(code):
    token_endpoint = "https://business-api.tiktok.com/open_api/v1.3/oauth2/access_token/"

    payload = {
        "app_id": CLIENT_ID,
        "secret": CLIENT_SECRET,
        "auth_code": code,
    }

    try:
        response = requests.post(token_endpoint, json=payload)
        response.raise_for_status()
        result = response.json()

        if result.get("code") == 0:
            return result.get("data")
        else:
            print(f"获取广告主访问令牌失败: {result.get('message')}")
            return None
    except Exception as e:
        print(f"请求异常: {str(e)}")
        return None


# 函数：换取TikTok原生账号访问令牌
def exchange_tiktok_token(code):
    token_endpoint = "https://open.tiktokapis.com/v1.3/tt_user/oauth2/token/"

    payload = {
        "client_id": CLIENT_ID,
        "client_secret": CLIENT_SECRET,
        "grant_type": "authorization_code",
        "auth_code": code,
        "redirect_uri": TIKTOK_REDIRECT_URI,
    }

    try:
        response = requests.post(token_endpoint, data=payload)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"获取TikTok访问令牌失败: {str(e)}")
        return None


# 路由：刷新TikTok访问令牌
@app.route("/authorize/refresh/tiktok", methods=["POST"])
def refresh_tiktok_token():
    token_id = request.json.get("token_id")
    if not token_id:
        return jsonify({"error": "Token ID is required"}), 400

    # 从数据库获取令牌
    token_doc = tokens_collection.find_one({"_id": ObjectId(token_id)})
    if not token_doc or token_doc.get("auth_type") != AUTH_TYPE_TIKTOK:
        return jsonify({"error": "Token not found or invalid type"}), 404

    refresh_token = token_doc["token_data"].get("refresh_token")
    if not refresh_token:
        return jsonify({"error": "Refresh token not available"}), 400

    # 刷新令牌
    token_endpoint = "https://open.tiktokapis.com/v1.3/tt_user/oauth2/refresh_token/"

    payload = {
        "client_key": CLIENT_ID,
        "client_secret": CLIENT_SECRET,
        "refresh_token": refresh_token,
        "grant_type": "refresh_token",
    }

    try:
        response = requests.post(token_endpoint, data=payload)
        response.raise_for_status()
        new_token_data = response.json()

        # 更新数据库中的令牌
        tokens_collection.update_one(
            {"_id": ObjectId(token_id)}, {"$set": {"token_data": new_token_data}}
        )

        return jsonify(
            {
                "message": "Token refreshed successfully",
                "token_summary": {
                    "access_token": new_token_data.get("access_token")[:10] + "...",
                    "expires_in": new_token_data.get("expires_in"),
                    "refresh_token": (
                        new_token_data.get("refresh_token")[:10] + "..."
                        if "refresh_token" in new_token_data
                        else None
                    ),
                    "refresh_expires_in": new_token_data.get("refresh_expires_in"),
                },
            }
        )
    except Exception as e:
        print(f"刷新TikTok访问令牌失败: {str(e)}")
        return jsonify({"error": "Failed to refresh token"}), 500


if __name__ == "__main__":
    app.run(debug=True)
