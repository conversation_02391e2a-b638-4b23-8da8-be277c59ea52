# AI-Agent-for-TikTok

## 合规数据接入

### 视频的数据结构需求(SLXu edit)

视频的逻辑结构：
```json
[
    {
        "hashTag": {
            "input": "gym",
            "searchHashtag": {
                "views": 902400000000,
                "name": "gym"
            }
        },
        "video": [
            {
                // 视频1信息
                "id": "7509602779850083614",
                "text": "Discipline > genetics #gym #aesthetics #gymmotivation ",
                "textLanguage": "en",
                "createTime": 1748465671,
                "createTimeISO": "2025-05-28T20:54:31.000Z",
                "isAd": false,
                "isMuted": false,
                "authorMeta": {
                    "id": "6938490373106861062",
                    "name": "shinsauce",
                    "profileUrl": "https://www.tiktok.com/@shinsauce",
                    "nickName": "joseph",
                    "verified": false,
                    "signature": "STRENGTH + LEAN aesthetics. DM “COACH” to start.\n@Breathedivinity Code: 'SHIN'",
                    "bioLink": null,
                    "originalAvatarUrl": "https://p19-pu-sign-useast8.tiktokcdn-us.com/tos-useast8-avt-0068-tx2/712d9d2d2c7cc15244adcc44ff0100d1~tplv-tiktokx-cropcenter:720:720.jpeg?dr=9640&refresh_token=73a16d96&x-expires=**********&x-signature=9eTJLQKcxBUqh08%2BMyArfjg6Yww%3D&t=4d5b0474&ps=********&shp=a5d48078&shcp=b59d6b55&idc=useast8",
                    "avatar": "https://p19-pu-sign-useast8.tiktokcdn-us.com/tos-useast8-avt-0068-tx2/712d9d2d2c7cc15244adcc44ff0100d1~tplv-tiktokx-cropcenter:720:720.jpeg?dr=9640&refresh_token=73a16d96&x-expires=**********&x-signature=9eTJLQKcxBUqh08%2BMyArfjg6Yww%3D&t=4d5b0474&ps=********&shp=a5d48078&shcp=b59d6b55&idc=useast8",
                    "privateAccount": false,
                    "ttSeller": false,
                    "following": 297,
                    "friends": 0,
                    "fans": 102500,
                    "heart": 6900000,
                    "video": 74,
                    "digg": 5626
                },
                "musicMeta": {
                    "musicName": "originalljud",
                    "musicAuthor": "JvstMikey",
                    "musicOriginal": true,
                    "musicAlbum": "",
                    "playUrl": "https://v16m.tiktokcdn-us.com/fdcc69f13e14d721ef0273cbf2e5fcdf/685e5cb4/video/tos/no1a/tos-no1a-v-2370-no/ooDMPBv8MBycHJEK4Chi36IwyIJEwiiGz5Aawf/?a=1233&bti=NDU3ZjAwOg%3D%3D&ch=0&cr=0&dr=0&er=2&cd=0%7C0%7C0%7C0&br=250&bt=125&ft=GSDrKInz7Thl5uDKXq8Zmo&mime_type=audio_mpeg&qs=6&rc=NjY3NDs7OmQ1aGk6OGhpNEBpMzx1cHI5cmhydzMzbzU8NUBjNV9jNTIxXy8xLjVjMDRjYSNsaGQwMmRjNXBgLS1kMTFzcw%3D%3D&vvpl=1&l=2025062702560177FEA2D1892EF9017371&btag=e000b8000&download=true",
                    "coverMediumUrl": "https://p16-common-sign-useast2a.tiktokcdn-us.com/tos-useast2a-avt-0068-euttp/53e989e301d3557d569b6495da6771ed~tplv-tiktokx-cropcenter:720:720.jpeg?dr=9640&refresh_token=6c8721fb&x-expires=**********&x-signature=zt9T93dzH48AzfMOcOqzIzSCV3Y%3D&t=4d5b0474&ps=********&shp=a5d48078&shcp=b59d6b55&idc=useast8",
                    "originalCoverMediumUrl": "https://p16-common-sign-useast2a.tiktokcdn-us.com/tos-useast2a-avt-0068-euttp/53e989e301d3557d569b6495da6771ed~tplv-tiktokx-cropcenter:720:720.jpeg?dr=9640&refresh_token=6c8721fb&x-expires=**********&x-signature=zt9T93dzH48AzfMOcOqzIzSCV3Y%3D&t=4d5b0474&ps=********&shp=a5d48078&shcp=b59d6b55&idc=useast8",
                    "musicId": "7456136019579374358"
                },
                "webVideoUrl": "https://www.tiktok.com/@shinsauce/video/7509602779850083614",
                "mediaUrls": [],
                "videoMeta": {
                    "height": 1024,
                    "width": 576,
                    "duration": 16,
                    "coverUrl": "https://p16-pu-sign-useast8.tiktokcdn-us.com/tos-useast8-p-0068-tx2/osAZAfQRDEBoFAmEFuEe3mIANCVQNG3AxWR0gA~tplv-tiktokx-origin.image?dr=9636&x-expires=**********&x-signature=zLl7aKIn9IdKMm0TTnZ2WiAUYi0%3D&t=4d5b0474&ps=********&shp=b59d6b55&shcp=43f4a2f9&idc=useast8",
                    "originalCoverUrl": "https://p16-pu-sign-useast8.tiktokcdn-us.com/tos-useast8-p-0068-tx2/osAZAfQRDEBoFAmEFuEe3mIANCVQNG3AxWR0gA~tplv-tiktokx-origin.image?dr=9636&x-expires=**********&x-signature=zLl7aKIn9IdKMm0TTnZ2WiAUYi0%3D&t=4d5b0474&ps=********&shp=b59d6b55&shcp=43f4a2f9&idc=useast8",
                    "definition": "540p",
                    "format": "mp4"
                },
                "diggCount": 87600,
                "shareCount": 3485,
                "playCount": 671400,
                "collectCount": 11500,
                "commentCount": 256,
                "mentions": [],
                "detailedMentions": [],
                "hashtags": [
                    {
                        "id": "7882",
                        "name": "gym",
                        "title": "From exercises to gym motivation and memes, show us how you workout!",
                        "cover": ""
                    },
                    {
                        "id": "47079",
                        "name": "aesthetics",
                        "title": "",
                        "cover": ""
                    },
                    {
                        "id": "117042",
                        "name": "gymmotivation",
                        "title": "",
                        "cover": ""
                    }
                ],
                "effectStickers": [],
                "isSlideshow": false,
                "isPinned": false,
                "isSponsored": false,
            },
            {
                // 视频2信息
            }
        ]
    }
]
```

单条视频的数据结构：
```json
    {
        "id": "7509602779850083614",
        "text": "Discipline > genetics #gym #aesthetics #gymmotivation ",
        "textLanguage": "en",
        "createTime": 1748465671,
        "createTimeISO": "2025-05-28T20:54:31.000Z",
        "isAd": false,
        "isMuted": false,
        "authorMeta": {
            "id": "6938490373106861062",
            "name": "shinsauce",
            "profileUrl": "https://www.tiktok.com/@shinsauce",
            "nickName": "joseph",
            "verified": false,
            "signature": "STRENGTH + LEAN aesthetics. DM “COACH” to start.\n@Breathedivinity Code: 'SHIN'",
            "bioLink": null,
            "originalAvatarUrl": "https://p19-pu-sign-useast8.tiktokcdn-us.com/tos-useast8-avt-0068-tx2/712d9d2d2c7cc15244adcc44ff0100d1~tplv-tiktokx-cropcenter:720:720.jpeg?dr=9640&refresh_token=73a16d96&x-expires=**********&x-signature=9eTJLQKcxBUqh08%2BMyArfjg6Yww%3D&t=4d5b0474&ps=********&shp=a5d48078&shcp=b59d6b55&idc=useast8",
            "avatar": "https://p19-pu-sign-useast8.tiktokcdn-us.com/tos-useast8-avt-0068-tx2/712d9d2d2c7cc15244adcc44ff0100d1~tplv-tiktokx-cropcenter:720:720.jpeg?dr=9640&refresh_token=73a16d96&x-expires=**********&x-signature=9eTJLQKcxBUqh08%2BMyArfjg6Yww%3D&t=4d5b0474&ps=********&shp=a5d48078&shcp=b59d6b55&idc=useast8",
            "privateAccount": false,
            "ttSeller": false,
            "following": 297,
            "friends": 0,
            "fans": 102500,
            "heart": 6900000,
            "video": 74,
            "digg": 5626
        },
        "musicMeta": {
            "musicName": "originalljud",
            "musicAuthor": "JvstMikey",
            "musicOriginal": true,
            "musicAlbum": "",
            "playUrl": "https://v16m.tiktokcdn-us.com/fdcc69f13e14d721ef0273cbf2e5fcdf/685e5cb4/video/tos/no1a/tos-no1a-v-2370-no/ooDMPBv8MBycHJEK4Chi36IwyIJEwiiGz5Aawf/?a=1233&bti=NDU3ZjAwOg%3D%3D&ch=0&cr=0&dr=0&er=2&cd=0%7C0%7C0%7C0&br=250&bt=125&ft=GSDrKInz7Thl5uDKXq8Zmo&mime_type=audio_mpeg&qs=6&rc=NjY3NDs7OmQ1aGk6OGhpNEBpMzx1cHI5cmhydzMzbzU8NUBjNV9jNTIxXy8xLjVjMDRjYSNsaGQwMmRjNXBgLS1kMTFzcw%3D%3D&vvpl=1&l=2025062702560177FEA2D1892EF9017371&btag=e000b8000&download=true",
            "coverMediumUrl": "https://p16-common-sign-useast2a.tiktokcdn-us.com/tos-useast2a-avt-0068-euttp/53e989e301d3557d569b6495da6771ed~tplv-tiktokx-cropcenter:720:720.jpeg?dr=9640&refresh_token=6c8721fb&x-expires=**********&x-signature=zt9T93dzH48AzfMOcOqzIzSCV3Y%3D&t=4d5b0474&ps=********&shp=a5d48078&shcp=b59d6b55&idc=useast8",
            "originalCoverMediumUrl": "https://p16-common-sign-useast2a.tiktokcdn-us.com/tos-useast2a-avt-0068-euttp/53e989e301d3557d569b6495da6771ed~tplv-tiktokx-cropcenter:720:720.jpeg?dr=9640&refresh_token=6c8721fb&x-expires=**********&x-signature=zt9T93dzH48AzfMOcOqzIzSCV3Y%3D&t=4d5b0474&ps=********&shp=a5d48078&shcp=b59d6b55&idc=useast8",
            "musicId": "7456136019579374358"
        },
        "webVideoUrl": "https://www.tiktok.com/@shinsauce/video/7509602779850083614",
        "mediaUrls": [],
        "videoMeta": {
            "height": 1024,
            "width": 576,
            "duration": 16,
            "coverUrl": "https://p16-pu-sign-useast8.tiktokcdn-us.com/tos-useast8-p-0068-tx2/osAZAfQRDEBoFAmEFuEe3mIANCVQNG3AxWR0gA~tplv-tiktokx-origin.image?dr=9636&x-expires=**********&x-signature=zLl7aKIn9IdKMm0TTnZ2WiAUYi0%3D&t=4d5b0474&ps=********&shp=b59d6b55&shcp=43f4a2f9&idc=useast8",
            "originalCoverUrl": "https://p16-pu-sign-useast8.tiktokcdn-us.com/tos-useast8-p-0068-tx2/osAZAfQRDEBoFAmEFuEe3mIANCVQNG3AxWR0gA~tplv-tiktokx-origin.image?dr=9636&x-expires=**********&x-signature=zLl7aKIn9IdKMm0TTnZ2WiAUYi0%3D&t=4d5b0474&ps=********&shp=b59d6b55&shcp=43f4a2f9&idc=useast8",
            "definition": "540p",
            "format": "mp4"
        },
        "diggCount": 87600,
        "shareCount": 3485,
        "playCount": 671400,
        "collectCount": 11500,
        "commentCount": 256,
        "mentions": [],
        "detailedMentions": [],
        "hashtags": [
            {
                "id": "7882",
                "name": "gym",
                "title": "From exercises to gym motivation and memes, show us how you workout!",
                "cover": ""
            },
            {
                "id": "47079",
                "name": "aesthetics",
                "title": "",
                "cover": ""
            },
            {
                "id": "117042",
                "name": "gymmotivation",
                "title": "",
                "cover": ""
            }
        ],
        "effectStickers": [],
        "isSlideshow": false,
        "isPinned": false,
        "isSponsored": false,
        "input": "gym",
        "searchHashtag": {
            "views": 902400000000,
            "name": "gym"
        }
    }
```


### 视频评论的数据结构需求(SLXu edit)

Input examaple
```bash
// 前端代码示例
fetch('http://localhost:3000/api/v1/analytics', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        description: 'Unleash Your Inner Power: The X99 GYM Experience, Designed for Women Tired of unwanted stares at the gym? Craving an exclusive space that truly belongs to women and understands their fitness needs? Welcome to X99! We deeply understand the unique requirements and psychological comfort of women's fitness. That's why we've specially crafted gender-separated training zones, allowing you to immerse yourself in your workout in a private, comfortable environment. Here, you'll bid farewell to the noise and discomfort of traditional gyms and embrace a fitness sanctuary truly centered around you. At X99, we advocate more than just fitness; we champion the awakening of strength. We tailor training programs for all women who aspire to be stronger and more confident. Whether it's sculpting elegant lines, boosting endurance, or challenging heavy weights to become a true muscle monster, our team of professional coaches will provide the most scientific and effective guidance. There's no judgment here, only support. Every woman in our community is an ally, collectively pursuing a healthier, more powerful self. Join X99, and you'll find: Exclusive Women's Training Zones: Absolute privacy and comfort. Female-Friendly Equipment: Designed to better suit women's physiology and training needs. Expert Female Coaches: Deeply understand women's physiology and psychology, helping you push your limits. Empowering Community: Grow together with like-minded sisters. At X99, let every workout be an opportunity to unleash your potential and sculpt yourself. Join us and become the muscle monster you were always meant to be!',
        comments: [
    {
        "videoWebUrl": "https://www.tiktok.com/@bellapoarch/video/6862153058223197445",
        "submittedVideoUrl": "https://www.tiktok.com/@bellapoarch/video/6862153058223197445",
        "input": "https://www.tiktok.com/@bellapoarch/video/6862153058223197445",
        "source": "Owner Own Video",
        "cid": "74938604972********",
        "createTime": **********,
        "createTimeISO": "2025-04-16T10:46:10.000Z",
        "text": "UPDATE TODAY!!\nbella poarch -69M\nleah halton -66M\nUno Edit -29.1M\nsuper crash guy -51.2M\nlip drawings -52.8M",
        "diggCount": 1937,
        "likedByAuthor": false,
        "pinnedByAuthor": false,
        "repliesToId": null,
        "replyCommentTotal": 0,
        "uid": "6620576143568601093",
        "uniqueId": "hudabubbaaa",
        "authorMeta": [
            {
                "account": {
                    "id": "6620576143568601093",
                    "name": "hudabubbaaa",
                    "profileUrl": "https://www.tiktok.com/@hudabubbaaa",
                    "nickName": "hudabubbaaa",
                    "verified": false,
                    "signature": "alright alright alright\<EMAIL>\n\n↓ online coaching ↓",
                    "bioLink": "https://linktr.ee/hudabubbaaa",
                    "originalAvatarUrl": "https://p16-pu-sign-useast8.tiktokcdn-us.com/tos-useast8-avt-0068-tx2/fde326c264361541613b9877de7dec5b~tplv-tiktokx-cropcenter:720:720.jpeg?dr=9640&refresh_token=7d46a662&x-expires=**********&x-signature=cK6fTTuqvA3hYv8D97dKg91TvP8%3D&t=4d5b0474&ps=********&shp=a5d48078&shcp=81f88b70&idc=useast5",
                    "avatar": "https://p16-pu-sign-useast8.tiktokcdn-us.com/tos-useast8-avt-0068-tx2/fde326c264361541613b9877de7dec5b~tplv-tiktokx-cropcenter:720:720.jpeg?dr=9640&refresh_token=7d46a662&x-expires=**********&x-signature=cK6fTTuqvA3hYv8D97dKg91TvP8%3D&t=4d5b0474&ps=********&shp=a5d48078&shcp=81f88b70&idc=useast5",
                    "commerceUserInfo": {
                        "commerceUser": false
                    },
                    "privateAccount": false,
                    "roomId": "",
                    "ttSeller": false,
                    "following": 332,
                    "friends": 177,
                    "fans": 2000000,
                    "heart": ********,
                    "video": 364,
                    "digg": 0
                },
                "video": [
                    {
                        "id": "7521862567938002207",
                        "text": "Mamacita Mode Activated 😜 #TEAMHUDA ",
                        "textLanguage": "en",
                        "createTime": **********,
                        "createTimeISO": "2025-06-30T21:48:33.000Z",
                        "isAd": false,
                        "musicMeta": {
                            "musicName": "original sound",
                            "musicAuthor": "hudabubbaaa",
                            "musicOriginal": true,
                            "playUrl": "https://v16m.tiktokcdn-us.com/adf150b71a54ee23e58416ac52d26c4b/6866814b/video/tos/useast8/tos-useast8-v-27dcd7-tx2/o4iAEqdfYjvELoKEZBM0aAiMMAqBiBAiBXyPjA/?a=1233&bti=ODszNWYuMDE6&ch=0&cr=0&dr=0&er=0&lr=default&cd=0%7C0%7C0%7C0&br=250&bt=125&ds=5&ft=GSDrKInz7Th-C0bKXq8Zmo&mime_type=audio_mpeg&qs=13&rc=M2lnO2w5cmZqNDMzaTU8NEBpM2lnO2w5cmZqNDMzaTU8NEAzY2VhMmRzYGVhLS1kMTJzYSMzY2VhMmRzYGVhLS1kMTJzcw%3D%3D&vvpl=1&l=20250703071008AD7B3490725B0B09E243&btag=e00078000",
                            "coverMediumUrl": "https://p19-pu-sign-useast8.tiktokcdn-us.com/tos-useast8-avt-0068-tx2/fde326c264361541613b9877de7dec5b~tplv-tiktokx-cropcenter:720:720.jpeg?dr=9640&refresh_token=50de8cb5&x-expires=**********&x-signature=47sIrAOrHtcxKuV8m9AAQklKHHo%3D&t=4d5b0474&ps=********&shp=a5d48078&shcp=81f88b70&idc=useast5",
                            "originalCoverMediumUrl": "https://p19-pu-sign-useast8.tiktokcdn-us.com/tos-useast8-avt-0068-tx2/fde326c264361541613b9877de7dec5b~tplv-tiktokx-cropcenter:720:720.jpeg?dr=9640&refresh_token=50de8cb5&x-expires=**********&x-signature=47sIrAOrHtcxKuV8m9AAQklKHHo%3D&t=4d5b0474&ps=********&shp=a5d48078&shcp=81f88b70&idc=useast5",
                            "musicId": "7521862535864830751"
                        },
                        "webVideoUrl": "https://www.tiktok.com/@hudabubbaaa/video/7521862567938002207",
                        "mediaUrls": [],
                        "videoMeta": {
                            "height": 1024,
                            "width": 576,
                            "duration": 26,
                            "coverUrl": "https://p16-pu-sign-useast8.tiktokcdn-us.com/tos-useast8-p-0068-tx2/ogH0MABdIiAiu16jfCAAIQ9iLYj0OExKjOMIqB~tplv-tiktokx-dmt-logom:tos-useast8-i-0068-tx2/o8DEnEWDAJACQYCFfAoREAAiImGnFYQBwvfDhV.image?dr=9634&x-expires=**********&x-signature=FnD%2F4awZ0vNFheZCfDOnsc93hgg%3D&t=4d5b0474&ps=********&shp=81f88b70&shcp=43f4a2f9&idc=useast5",
                            "originalCoverUrl": "https://p16-pu-sign-useast8.tiktokcdn-us.com/tos-useast8-p-0068-tx2/ogH0MABdIiAiu16jfCAAIQ9iLYj0OExKjOMIqB~tplv-tiktokx-dmt-logom:tos-useast8-i-0068-tx2/o8DEnEWDAJACQYCFfAoREAAiImGnFYQBwvfDhV.image?dr=9634&x-expires=**********&x-signature=FnD%2F4awZ0vNFheZCfDOnsc93hgg%3D&t=4d5b0474&ps=********&shp=81f88b70&shcp=43f4a2f9&idc=useast5",
                            "definition": "540p",
                            "format": "mp4"
                        },
                        "diggCount": 130500,
                        "shareCount": 1978,
                        "playCount": 2300000,
                        "collectCount": 3734,
                        "commentCount": 4490,
                        "mentions": [],
                        "detailedMentions": [],
                        "hashtags": [
                            {
                                "name": "teamhuda"
                            }
                        ],
                        "effectStickers": [
                            {
                                "ID": "1508704914",
                                "name": "Bratz Idol Glow",
                                "stickerStats": {
                                    "useCount": 0
                                }
                            }
                        ],
                        "isSlideshow": false,
                        "isPinned": false,
                        "isSponsored": false,
                        "input": "hudabubbaaa",
                        "fromProfileSection": "videos"
                    }
                ]
            }
        ],
        "avatarThumbnail": "https://p16-common-sign-va.tiktokcdn-us.com/tos-maliva-avt-0068/f56a5d386e6672f745d1b8ab40b7ef19~tplv-tiktokx-cropcenter:100:100.jpg?dr=9640&refresh_token=fb74f73a&x-expires=1751439600&x-signature=QsAAO%2BIc3M9oWmc%2FDUhwBFUVG0c%3D&t=4d5b0474&ps=********&shp=30310797&shcp=ff37627b&idc=useast5",
        "mentions": [
            "leah halton",
            "Uno Edit"
        ],
        "detailedMentions": [
            {
                "nickName": "eah halton",
                "profileUrl": "https://www.tiktok.com/@undefined"
            },
            {
                "nickName": "no Edit",
                "profileUrl": "https://www.tiktok.com/@undefined"
            }
        ]
    }
],
        hashTagVideos: [{}],
        w_1: 0.4,
        w_2: 0.3,
        w_3: 0.2,
        w_4: 0.1
    })
})
.then(res => res.json())
.then(data => {
    console.log('后端返回结果:', data);
});
```


账户的基础信息，**一定要包括```fans```和```hearts```**，
账户的创作视频，**只要近30天的创作视频**



## 智能线索挖掘引擎

### 三级意图过滤机制

**关键字初筛**静态关键词表过滤

**语义分析**Gemini识别购买意图
    - 自有视频下评论 & 竞品视频下评论的意图识别
      - 自有视频下正向的 / 竞品视频下负向的 -> 高意图
    - 关键词视频的意图识别
      - 可以通过tagHash以及视频基本信息识别意图
      - 进一步地，可以通过视频理解识别意图

**情景强化**Gemini语义绑定评论者需求，绑定创作者创作点
    - 评论分析时绑定潜客需求点
    - 创作分析时绑定创作点(如健身挑战的#练腿，#核心暴汗)

### 账号画像分析

**提取商业兴趣标签**
    - 评论者账号提取商业兴趣标签
      - 有时候分析不出来，这很正常，很多消费者账号不发视频，也不写标签
    - 创作者账号提取商业兴趣标签
      - 个人资料
      - 创作内容

**近期活跃带单视频**
    - 评论者账号大概率没有
    - 创作者账号筛选前30天的活跃度高的带单视频
  
### 时效分

### 地理匹配分

