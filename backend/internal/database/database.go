package database

import (
	"context"
	"fmt"
	"os"

	"server/config"
	"server/config/db"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"

	_ "github.com/golang-migrate/migrate/v4/database/postgres"
)

var DB *pgx.Conn
var Pool *pgxpool.Pool
var DBCtx = context.Background()

func NewQuery() *db.Queries {
	return db.New(Pool)
}

func ConnectDB() {
	connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		config.C.Postgres.Host,
		config.C.Postgres.Port,
		config.C.Postgres.User,
		config.C.Postgres.Password,
		config.C.Postgres.DBName,
	)

	pool, err := pgxpool.New(DBCtx, connStr)
	if err != nil {
		fmt.Fprintf(os.<PERSON>derr, "Unable to connect to database: %v\n", err)
		os.Exit(1)
	}
	// DB = db
	Pool = pool
}
