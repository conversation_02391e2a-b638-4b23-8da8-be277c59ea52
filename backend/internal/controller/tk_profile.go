package controller

import (
	"context"
	"encoding/json"
	"fmt"
	"server/config"
	"server/config/db"
	"server/internal/database"
	"server/internal/utils"
	"server/logger"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
)

type TKProfileController struct{}

type TikTokProfileRequest struct {
	TikTokBasicRequest
	Profiles []string `json:"profiles"`
}

func (tk *TKProfileController) RegisterRoutes(rg *gin.RouterGroup) {
	r := rg.Group("/v1")
	r.POST("/tk_profile", tk.Create)
	r.GET("/tk_profile", tk.GetPaged)
}

func (tk *TKProfileController) Create(c *gin.Context) {
	var req TikTokProfileRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request body"})
		return
	}

	me, _ := c.Get("me")
	user, _ := me.(db.User)
	q := database.NewQuery()

	apifyUrl := fmt.Sprintf("%s%s", config.C.ApiFyProfileUrl, config.C.ApifyApiKey)
	client := resty.New()

	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(req).
		Post(apifyUrl)

	if err != nil {
		fmt.Println("Error calling Apify API:", err)
		c.JSON(500, gin.H{"error": "Failed to call Apify API"})
		return
	}

	fmt.Println("Response Body:", resp.String())

	var videos []map[string]interface{}
	if err := json.Unmarshal([]byte(resp.String()), &videos); err != nil {
		c.JSON(500, gin.H{"error": "Failed to parse Apify response"})
		return
	}

	for _, v := range videos {
		author := v["authorMeta"].(map[string]interface{})
		musicRaw, ok := v["musicMeta"]
		if !ok || musicRaw == nil {
			fmt.Println("musicMeta is missing, skip video:", v["id"])
			continue
		}

		music, ok := musicRaw.(map[string]interface{})
		if !ok {
			fmt.Println("musicMeta is not a map, skip video:", v["id"])
			continue
		}
		videoID := v["id"].(string)

		// insert video
		createUnix := int64(utils.GetFloat(v["createTime"]))
		videoMetaRaw, ok := v["videoMeta"]
		if !ok || videoMetaRaw == nil {
			fmt.Println("videoMeta is missing, skip video:", v["id"])
			continue
		}

		videoMeta, ok := videoMetaRaw.(map[string]interface{})
		if !ok {
			fmt.Println("videoMeta is not a map, skip video:", v["id"])
			continue
		}

		// insert video author if not exists
		_ = q.CreateTKVideoAuthor(c, db.CreateTKVideoAuthorParams{
			ID:                utils.GetString(author, "id"),
			Username:          utils.StringToPGText(utils.GetString(author, "name")),
			Nickname:          utils.StringToPGText(utils.GetString(author, "nickName")),
			ProfileUrl:        utils.StringToPGText(utils.GetString(author, "profileUrl")),
			AvatarUrl:         utils.StringToPGText(utils.GetString(author, "avatar")),
			OriginalAvatarUrl: utils.StringToPGText(utils.GetString(author, "originalAvatarUrl")),
			Signature:         utils.StringToPGText(utils.GetString(author, "signature")),
			BioLink:           utils.StringToPGText(utils.GetString(author, "bioLink")),
			Verified:          utils.PgBool(utils.GetBool(author, "verified")),
			PrivateAccount:    utils.PgBool(utils.GetBool(author, "privateAccount")),
			Region:            utils.StringToPGText(utils.GetString(author, "region")),
			Fans:              utils.PgInt4(int(utils.GetFloat(author["fans"]))),
			Heart:             utils.PgInt8(int64(utils.GetFloat(author["heart"]))),
			Following:         utils.PgInt4(int(utils.GetFloat(author["following"]))),
			Friends:           utils.PgInt4(int(utils.GetFloat(author["friends"]))),
			VideoCount:        utils.PgInt4(int(utils.GetFloat(author["video"]))),
			DiggCount:         utils.PgInt4(0),
		})

		// insert video music if not exists
		_ = q.CreateTKVideoMusic(c, db.CreateTKVideoMusicParams{
			ID:               music["musicId"].(string),
			Name:             utils.StringToPGText(music["musicName"].(string)),
			Author:           utils.StringToPGText(music["musicAuthor"].(string)),
			IsOriginal:       utils.PgBool(music["musicOriginal"].(bool)),
			PlayUrl:          utils.StringToPGText(music["playUrl"].(string)),
			CoverUrl:         utils.StringToPGText(music["coverMediumUrl"].(string)),
			OriginalCoverUrl: utils.StringToPGText(music["originalCoverMediumUrl"].(string)),
		})

		// Inside the Create method, replace the CreateTKVideo call with this:
		_ = q.CreateTKVideo(c, db.CreateTKVideoParams{
			ID:           videoID,
			AuthorID:     author["id"].(string),
			MusicID:      utils.StringToPGText(music["musicId"].(string)),
			Text:         utils.StringToPGText(v["text"].(string)),
			TextLanguage: utils.StringToPGText(v["textLanguage"].(string)),
			UserID:       int32(user.ID),
			CreateTime:   utils.PgTimestamp(time.Unix(createUnix, 0)),
			IsAd:         utils.PgBool(v["isAd"].(bool)),
			VideoUrl:     utils.StringToPGText(v["webVideoUrl"].(string)),
			Height:       utils.PgInt4(int(utils.GetFloat(videoMeta["height"]))),
			Width:        utils.PgInt4(int(utils.GetFloat(videoMeta["width"]))),
			Duration:     utils.PgInt4(int(utils.GetFloat(videoMeta["duration"]))),
			Definition:   utils.StringToPGText(utils.GetString(videoMeta, "definition")), // Use GetString to safely handle nil or non-string values
			CoverUrl:     utils.StringToPGText(utils.GetString(videoMeta, "coverUrl")),
			DiggCount:    utils.PgInt4(int(utils.GetFloat(v["diggCount"]))),
			ShareCount:   utils.PgInt4(int(utils.GetFloat(v["shareCount"]))),
			PlayCount:    utils.PgInt8(int64(utils.GetFloat(v["playCount"]))),
			CollectCount: utils.PgInt4(int(utils.GetFloat(v["collectCount"]))),
			CommentCount: utils.PgInt4(int(utils.GetFloat(v["commentCount"]))),
			IsPinned:     utils.PgBool(v["isPinned"].(bool)),
			IsSponsored:  utils.PgBool(v["isSponsored"].(bool)),
			IsSlideshow:  utils.PgBool(v["isSlideshow"].(bool)),
			ExpiresAt:    utils.PgTimestamp(time.Now().Add(7 * 24 * time.Hour)),
		})

		// insert hashtags
		for _, h := range v["hashtags"].([]interface{}) {
			tag := h.(map[string]interface{})["name"].(string)
			_ = q.CreateTKVideoHashtagsAndMentions(c, db.CreateTKVideoHashtagsAndMentionsParams{
				VideoID: videoID,
				Hashtag: tag,
			})
		}

		// insert mentions
		for _, m := range v["mentions"].([]interface{}) {
			username := m.(string)
			_ = q.CreateTKVideoMentions(c, db.CreateTKVideoMentionsParams{
				VideoID:       videoID,
				MentionedUser: username,
			})
		}
	}

	c.JSON(200, gin.H{
		"message": "Profile data saved successfully",
		"data":    videos,
	})
}

func (tk *TKProfileController) Destroy(c *gin.Context) {
	panic("not implemented") // TODO: Implement
}

func (tk *TKProfileController) Update(c *gin.Context) {
	panic("not implemented") // TODO: Implement
}

func (tk *TKProfileController) Get(c *gin.Context) {
	panic("not implemented") // TODO: Implement
}

func (tk *TKProfileController) GetPaged(c *gin.Context) {
	me, _ := c.Get("me")
	user, ok := me.(db.User)
	if !ok {
		c.JSON(401, gin.H{"error": "unauthorized"})
		return
	}

	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "10")

	page, _ := strconv.Atoi(pageStr)
	pageSize, _ := strconv.Atoi(pageSizeStr)
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	q := database.NewQuery()
	videos, err := q.ListTKVideosWithDetails(c, db.ListTKVideosWithDetailsParams{
		UserID: int32(user.ID),
		Limit:  int32(pageSize),
		Offset: int32(offset),
	})
	if err != nil {
		c.JSON(500, gin.H{"error": "failed to get videos"})
		return
	}

	c.JSON(200, gin.H{
		"page":     page,
		"pageSize": pageSize,
		"data":     videos,
	})
}

func CrawlTikTokProfiles(ctx context.Context, profiles []string, user db.User) ([]db.TiktokAuthor, error) {
	apifyUrl := fmt.Sprintf("%s%s", config.C.ApiFyProfileUrl, config.C.ApifyApiKey)
	client := resty.New()

	req := TikTokProfileRequest{
		TikTokBasicRequest: TikTokBasicRequest{
			ExcludePinnedPosts:            false,
			ResultsPerPage:                1,
			ScrapeRelatedVideos:           false,
			ShouldDownloadAvatars:         false,
			ShouldDownloadCovers:          false,
			ShouldDownloadMusicCovers:     false,
			ShouldDownloadSlideshowImages: false,
			ShouldDownloadSubtitles:       false,
			ShouldDownloadVideos:          false,
			ProfileScrapeSections:         []string{"videos"},
			ProfileSorting:                "latest",
			SearchSection:                 "",
			MaxProfilesPerQuery:           10,
		},
		Profiles: profiles,
	}

	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(req).
		Post(apifyUrl)
	if err != nil {
		logger.Error("err", err)
		return nil, err
	}

	var videos []map[string]interface{}
	if err := json.Unmarshal([]byte(resp.String()), &videos); err != nil {
		return nil, err
	}

	q := database.NewQuery()
	result := make([]db.TiktokAuthor, 0)

	for _, v := range videos {

		authorRaw, ok := v["authorMeta"]
		if !ok {
			continue
		}

		author, ok := authorRaw.(map[string]interface{})
		if !ok || author == nil {
			continue
		}

		authorID := utils.GetString(author, "id")

		err := q.CreateTKVideoAuthor(ctx, db.CreateTKVideoAuthorParams{
			ID:                authorID,
			Username:          utils.StringToPGText(utils.GetString(author, "name")),
			Nickname:          utils.StringToPGText(utils.GetString(author, "nickName")),
			ProfileUrl:        utils.StringToPGText(utils.GetString(author, "profileUrl")),
			AvatarUrl:         utils.StringToPGText(utils.GetString(author, "avatar")),
			OriginalAvatarUrl: utils.StringToPGText(utils.GetString(author, "originalAvatarUrl")),
			Signature:         utils.StringToPGText(utils.GetString(author, "signature")),
			BioLink:           utils.StringToPGText(utils.GetString(author, "bioLink")),
			Verified:          utils.PgBool(utils.GetBool(author, "verified")),
			PrivateAccount:    utils.PgBool(utils.GetBool(author, "privateAccount")),
			Region:            utils.StringToPGText(utils.GetString(author, "region")),
			Fans:              utils.PgInt4(int(utils.GetFloat(author["fans"]))),
			Heart:             utils.PgInt8(int64(utils.GetFloat(author["heart"]))),
			Following:         utils.PgInt4(int(utils.GetFloat(author["following"]))),
			Friends:           utils.PgInt4(int(utils.GetFloat(author["friends"]))),
			VideoCount:        utils.PgInt4(int(utils.GetFloat(author["video"]))),
			DiggCount:         utils.PgInt4(0),
		})
		if err != nil {
			return nil, err
		}

		result = append(result, db.TiktokAuthor{
			ID:                authorID,
			Username:          utils.StringToPGText(utils.GetString(author, "name")),
			Nickname:          utils.StringToPGText(utils.GetString(author, "nickName")),
			ProfileUrl:        utils.StringToPGText(utils.GetString(author, "profileUrl")),
			AvatarUrl:         utils.StringToPGText(utils.GetString(author, "avatar")),
			OriginalAvatarUrl: utils.StringToPGText(utils.GetString(author, "originalAvatarUrl")),
			Signature:         utils.StringToPGText(utils.GetString(author, "signature")),
			BioLink:           utils.StringToPGText(utils.GetString(author, "bioLink")),
			Verified:          utils.PgBool(utils.GetBool(author, "verified")),
			PrivateAccount:    utils.PgBool(utils.GetBool(author, "privateAccount")),
			Region:            utils.StringToPGText(utils.GetString(author, "region")),
			Fans:              utils.PgInt4(int(utils.GetFloat(author["fans"]))),
			Heart:             utils.PgInt8(int64(utils.GetFloat(author["heart"]))),
			Following:         utils.PgInt4(int(utils.GetFloat(author["following"]))),
			Friends:           utils.PgInt4(int(utils.GetFloat(author["friends"]))),
			VideoCount:        utils.PgInt4(int(utils.GetFloat(author["video"]))),
			DiggCount:         utils.PgInt4(0),
		})
	}

	return result, nil
}
