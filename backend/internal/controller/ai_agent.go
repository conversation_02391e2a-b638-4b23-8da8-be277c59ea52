package controller

import (
	"encoding/json"
	"net/http"
	"server/config"
	"server/config/db"
	"server/internal/database"
	"server/internal/utils"
	"server/logger"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
)

type AIAgentController struct{}

type SelectedPostsRequest struct {
	CommentIDs      []string `json:"commentIds"`
	ProfileVideoIDs []string `json:"profileVideoIDs"`
	VideoHashTags   []string `json:"videoHashTags"`
	Description     string   `json:"description"`
}

type AiAgentCommentReq struct {
	Comments    []interface{} `json:"comments"`
	Description string        `json:"description"`
}
type AiAgentProfileVideoReq struct {
	ProfileVideoIDs []string `json:"profileVideoIDs"`
	Description     string   `json:"description"`
}
type AiAgentHashTagReq struct {
	VideoHashTags []TKVideo `json:"hashTagVideos"`
	Description   string    `json:"description"`
}

type TKCommentWithSourceAndProfile struct {
	db.TiktokComment
	Source  string                  `json:"source"`
	Profile *TiktokAuthorWithVideos `json:"profile,omitempty"`
}

type TKVideo struct {
	ID            string `json:"id"`
	Text          string `json:"text"`
	TextLanguage  string `json:"text_language"`
	UserID        string `json:"user_id"`
	VideoUrl      string `json:"video_url"`
	CoverUrl      string `json:"cover_url"`
	Width         int32  `json:"width"`
	Height        int32  `json:"height"`
	Duration      int32  `json:"duration"`
	Definition    string `json:"definition"`
	CreateTime    int64  `json:"create_time"`
	CreateTimeIso string `json:"create_time_iso"`
	Input         string `json:"input"`
	SearchHashtag string `json:"search_hashtag"`
	SearchViews   int32  `json:"search_hashtag_views"`
	CommentCount  int32  `json:"comment_count"`
	DiggCount     int32  `json:"digg_count"`
	ShareCount    int32  `json:"share_count"`
	PlayCount     int32  `json:"play_count"`
	CollectCount  int32  `json:"collect_count"`
	IsAd          bool   `json:"is_ad"`
	IsPinned      bool   `json:"is_pinned"`
	IsSponsored   bool   `json:"is_sponsored"`
	IsSlideshow   bool   `json:"is_slideshow"`
	ExpiresAt     int64  `json:"expires_at"`

	Profile Profile `json:"profile"`
	Music   Music   `json:"music"`
}

type Profile struct {
	ID                string `json:"id"`
	Username          string `json:"username"`
	Nickname          string `json:"nickname"`
	ProfileUrl        string `json:"profile_url"`
	AvatarUrl         string `json:"avatar_url"`
	OriginalAvatarUrl string `json:"original_avatar_url"`
	Signature         string `json:"signature"`
	BioLink           string `json:"bio_link"`
	Verified          bool   `json:"verified"`
	PrivateAccount    bool   `json:"private_account"`
	Region            string `json:"region"`
	Fans              int32  `json:"fans"`
	Heart             int64  `json:"heart"`
	Following         int32  `json:"following"`
	Friends           int32  `json:"friends"`
	VideoCount        int32  `json:"video_count"`
	DiggCount         int32  `json:"digg_count"`
}

type Music struct {
	ID               string `json:"id"`
	Name             string `json:"name"`
	Author           string `json:"author"`
	IsOriginal       bool   `json:"is_original"`
	PlayUrl          string `json:"play_url"`
	CoverUrl         string `json:"cover_url"`
	OriginalCoverUrl string `json:"original_cover_url"`
}

type TiktokAuthorWithVideos struct {
	db.TiktokAuthor
	Videos []db.TiktokVideo `json:"videos"`
}

func (a *AIAgentController) RegisterRoutes(rg *gin.RouterGroup) {
	r := rg.Group("/v1")
	r.POST("/ai_agent", a.Create)
	r.GET("/analytics", a.GetAnalyticsPage)
}

func (a *AIAgentController) Create(c *gin.Context) {
	var req SelectedPostsRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid JSON"})
		return
	}

	q := database.NewQuery()
	me, _ := c.Get("me")
	user, _ := me.(db.User)

	if len(req.CommentIDs) > 0 {
		logger.Info("Received comment IDs:", "comment id ", req.CommentIDs)
		list, _ := q.ListTKCommentsByIDs(database.DBCtx, req.CommentIDs)
		profileIDs := make([]string, 0, len(list))
		for _, item := range list {
			profileIDs = append(profileIDs, item.AuthorUniqueID.String)
		}

		profileMap := make(map[string]*TiktokAuthorWithVideos)

		if len(profileIDs) > 0 {

			profiles, _ := q.ListTKAuthorsByIDs(database.DBCtx, profileIDs)

			existingIDs := make(map[string]bool)
			for _, p := range profiles {
				existingIDs[p.Username.String] = true
			}

			missingIDs := make([]string, 0)
			for _, id := range profileIDs {
				if !existingIDs[id] {
					missingIDs = append(missingIDs, id)
				}
			}

			if len(missingIDs) > 0 {
				crawledProfiles, err := CrawlTikTokProfiles(database.DBCtx, missingIDs, user)
				if err != nil {
					c.JSON(500, gin.H{"error": err.Error()})
					return
				}
				profiles = append(profiles, crawledProfiles...)
			}
			for _, p := range profiles {
				videos, _ := q.ListTKAuthorsByAuthorIDs(database.DBCtx, []string{p.ID})
				profileMap[p.Username.String] = &TiktokAuthorWithVideos{
					TiktokAuthor: p,
					Videos:       videos,
				}
			}
		}
		result := make([]TKCommentWithSourceAndProfile, len(list))
		for i, item := range list {
			authorId := extractAuthorIDFromUrl(item.VideoUrl)
			source := "Competitor Video"
			if contains(user.AuthorUniqueIds, authorId) {
				source = "Our Own Video"
			}
			result[i] = TKCommentWithSourceAndProfile{
				TiktokComment: item,
				Source:        source,
				Profile:       profileMap[item.AuthorUniqueID.String],
			}
		}

		commentsInterface := make([]interface{}, len(result))
		for i, item := range result {
			commentsInterface[i] = item
		}

		aiReq := AiAgentCommentReq{
			Comments:    commentsInterface,
			Description: req.Description,
		}
		res, _ := reqAiAgent(aiReq)

		var reutrnResult map[string]interface{}
		if err := json.Unmarshal([]byte(res.String()), &reutrnResult); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid AI response"})
			return
		}

		handleAiAnalyticsResponse(q, &user, reutrnResult, c)

		c.JSON(http.StatusOK, gin.H{
			"req":  aiReq,
			"data": reutrnResult,
		})
		return

	} else if len(req.ProfileVideoIDs) > 0 {
		logger.Info("Received ProfileVideoIDs IDs:", "Received video IDs", req.ProfileVideoIDs)
		aiReq := AiAgentProfileVideoReq{
			ProfileVideoIDs: req.ProfileVideoIDs,
			Description:     req.Description,
		}
		res, _ := reqAiAgent(aiReq)
		c.JSON(http.StatusOK, gin.H{"data": res.String()})
		return

	} else if len(req.VideoHashTags) > 0 {
		logger.Info("Received VideoHashTags IDs:", "Received video IDs", req.VideoHashTags)

		var videos []TKVideo
		for _, videoID := range req.VideoHashTags {
			video, err := q.GetTKVideoWithAuthorAndMusic(database.DBCtx, videoID)
			if err != nil {
				logger.Error("Failed to get video:", "videoID", videoID, "err", err)
				continue
			}
			videos = append(videos, formatTKVideo(video))
		}
		aiReq := AiAgentHashTagReq{
			VideoHashTags: videos,
			Description:   req.Description,
		}

		res, _ := reqAiAgent(aiReq)

		var reutrnResult map[string]interface{}
		if err := json.Unmarshal([]byte(res.String()), &reutrnResult); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid AI response"})
			return
		}

		handleAiAnalyticsResponse(q, &user, reutrnResult, c)

		c.JSON(http.StatusOK, gin.H{"data": reutrnResult, "req": aiReq})
		return

	} else {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No IDs provided"})
		return
	}

}

func (a *AIAgentController) Destroy(c *gin.Context) {
	panic("not implemented") // TODO: Implement
}

func (a *AIAgentController) Update(c *gin.Context) {
	panic("not implemented") // TODO: Implement
}

func (a *AIAgentController) Get(c *gin.Context) {
	panic("not implemented") // TODO: Implement
}

func (a *AIAgentController) GetPaged(c *gin.Context) {
	panic("not implemented") // TODO: Implement
}

func (a *AIAgentController) GetAnalyticsPage(c *gin.Context) {
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("pageSize", "20")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}
	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize

	me, _ := c.Get("me")
	user, ok := me.(db.User)
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	q := database.NewQuery()

	comments, err := q.ListAISortedComments(database.DBCtx, db.ListAISortedCommentsParams{
		UserID: int32(user.ID),
		Limit:  int32(pageSize),
		Offset: int32(offset),
	})
	if err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}

	kocs, err := q.ListAISortedKocs(database.DBCtx, db.ListAISortedKocsParams{
		UserID: int32(user.ID),
		Limit:  int32(pageSize),
		Offset: int32(offset),
	})
	if err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"page":     page,
		"pageSize": pageSize,
		"data": gin.H{
			"comments": comments,
			"kocs":     kocs,
		},
	})
}

func reqAiAgent(body interface{}) (*resty.Response, error) {
	client := resty.New()
	aiAnalyticsURL := config.C.AiAnalytics

	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(body).
		Post(aiAnalyticsURL)

	if err != nil {
		logger.Error("err", err)
		return nil, err
	}

	return resp, nil
}

func extractAuthorIDFromUrl(url string) string {
	start := strings.Index(url, "@")
	if start == -1 {
		return ""
	}
	start += 1
	end := strings.Index(url[start:], "/")
	if end == -1 {
		return ""
	}
	return url[start : start+end]
}

func contains(arr []string, val string) bool {
	for _, v := range arr {
		if v == val {
			return true
		}
	}
	return false
}

func formatTKVideo(row db.GetTKVideoWithAuthorAndMusicRow) TKVideo {

	return TKVideo{
		ID:            row.ID,
		Text:          row.Text.String,
		TextLanguage:  row.TextLanguage.String,
		UserID:        string(row.UserID),
		VideoUrl:      row.VideoUrl.String,
		CoverUrl:      row.CoverUrl.String,
		Width:         row.Width.Int32,
		Height:        row.Height.Int32,
		Duration:      row.Duration.Int32,
		Definition:    row.Definition.String,
		CreateTime:    row.CreateTime.Time.Unix(),
		CreateTimeIso: row.CreateTimeIso.Time.Format("2006-01-02T15:04:05Z"),
		Input:         row.Input.String,
		SearchHashtag: row.SearchHashtag.String,
		SearchViews:   int32(row.SearchHashtagViews.Int64),
		CommentCount:  row.CommentCount.Int32,
		DiggCount:     row.DiggCount.Int32,
		ShareCount:    row.ShareCount.Int32,
		PlayCount:     int32(row.PlayCount.Int64),
		CollectCount:  row.CollectCount.Int32,
		IsAd:          row.IsAd.Bool,
		IsPinned:      row.IsPinned.Bool,
		IsSponsored:   row.IsSponsored.Bool,
		IsSlideshow:   row.IsSlideshow.Bool,
		ExpiresAt:     row.ExpiresAt.Time.Unix(),
		Profile: Profile{
			ID:                row.AuthorID.String,
			Username:          row.Username.String,
			Nickname:          row.Nickname.String,
			ProfileUrl:        row.ProfileUrl.String,
			AvatarUrl:         row.AvatarUrl.String,
			OriginalAvatarUrl: row.OriginalAvatarUrl.String,
			Signature:         row.Signature.String,
			BioLink:           row.BioLink.String,
			Verified:          row.Verified.Bool,
			PrivateAccount:    row.PrivateAccount.Bool,
			Region:            row.Region.String,
			Fans:              row.Fans.Int32,
			Heart:             row.Heart.Int64,
			Following:         row.Following.Int32,
			Friends:           row.Friends.Int32,
			VideoCount:        row.VideoCount.Int32,
			DiggCount:         row.AuthorDiggCount.Int32,
		},
		Music: Music{
			ID:               row.MusicID.String,
			Name:             row.MusicName.String,
			Author:           row.MusicAuthor.String,
			IsOriginal:       row.IsOriginal.Bool,
			PlayUrl:          row.PlayUrl.String,
			CoverUrl:         row.MusicCoverUrl.String,
			OriginalCoverUrl: row.OriginalCoverUrl.String,
		},
	}
}

func handleAiAnalyticsResponse(q *db.Queries, me *db.User, reutrnResult map[string]interface{}, c *gin.Context) {
	sortedComments, ok := reutrnResult["sortedComments"].([]interface{})
	if !ok {
		c.JSON(500, gin.H{"error": "Invalid sortedComments"})
		return
	}

	sortedKocs, ok := reutrnResult["sortedKocs"].([]interface{})
	if !ok {
		c.JSON(500, gin.H{"error": "Invalid sortedKocs"})
		c.Abort()
		return
	}

	// Save sortedComments
	for _, item := range sortedComments {
		comment := item.(map[string]interface{})

		var labels []string
		if labelsRaw, ok := comment["labels"]; ok && labelsRaw != nil {
			if arr, ok := labelsRaw.([]interface{}); ok {
				for _, l := range arr {
					if str, ok := l.(string); ok {
						labels = append(labels, str)
					}
				}
			}
		}

		var urls []string
		if urlsRaw, ok := comment["urls"]; ok && urlsRaw != nil {
			if arr, ok := urlsRaw.([]interface{}); ok {
				for _, u := range arr {
					if str, ok := u.(string); ok {
						urls = append(urls, str)
					}
				}
			}
		}

		err := q.InsertAISortedComment(database.DBCtx, db.InsertAISortedCommentParams{
			VideoUrl:              utils.GetString(comment, "video_url"),
			UserID:                int32(me.ID),
			Text:                  utils.StringToPGText(utils.GetString(comment, "text")),
			IntentScore:           utils.PgInt4(int(comment["intent_score"].(float64))),
			Labels:                labels,
			Urls:                  urls,
			FinalScore:            utils.PgFloat8(comment["final_score"].(float64)),
			ScenarioReinforcement: utils.StringToPGText(utils.GetString(comment, "scenario_reinforcement")),
		})
		if err != nil {
			logger.Error("Failed to insert comment:", err)
			return
		}

	}
	// Save sortedKocs
	for _, item := range sortedKocs {
		koc := item.(map[string]interface{})

		labels := []string{}
		if labelsRaw, ok := koc["labels"]; ok && labelsRaw != nil {
			if arr, ok := labelsRaw.([]interface{}); ok {
				for _, l := range arr {
					if str, ok := l.(string); ok {
						labels = append(labels, str)
					}
				}
			}
		}

		urls := []string{}
		if urlsRaw, ok := koc["urls"]; ok && urlsRaw != nil {
			if arr, ok := urlsRaw.([]interface{}); ok {
				for _, u := range arr {
					if str, ok := u.(string); ok {
						urls = append(urls, str)
					}
				}
			}
		}

		intentScore := 0
		if val, ok := koc["intent_score"]; ok && val != nil {
			if f, ok := val.(float64); ok {
				intentScore = int(f)
			}
		}
		finalScore := 0.0
		if val, ok := koc["final_score"]; ok && val != nil {
			if f, ok := val.(float64); ok {
				finalScore = f
			}
		}

		err := q.InsertAISortedKoc(database.DBCtx, db.InsertAISortedKocParams{
			VideoUrl:              utils.GetString(koc, "video_url"),
			UserID:                int32(me.ID),
			IntentScore:           utils.PgInt4(intentScore),
			Labels:                labels,
			Urls:                  urls,
			FinalScore:            utils.PgFloat8(finalScore),
			ScenarioReinforcement: utils.StringToPGText(utils.GetString(koc, "scenario_reinforcement")),
		})
		if err != nil {
			logger.Error("Failed to insert koc:", err)
		}
	}
}
