package controller

import (
	"encoding/json"
	"fmt"
	"server/config"
	"server/config/db"
	"server/internal/database"
	"server/internal/utils"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
)

type TKVideoController struct{}

type TikTokHasTagRequest struct {
	TikTokBasicRequest
	Hashtags []string `json:"hashtags"`
}

func (tk *TKVideoController) RegisterRoutes(rg *gin.RouterGroup) {
	r := rg.Group("/v1")
	r.POST("/tk_video_with_hashtags", tk.Create)
	r.GET("/tk_video/:id", tk.Get)
	r.GET("/tk_video_with_hashtags", tk.GetPaged)
}

func (tk *TKVideoController) Create(c *gin.Context) {
	var req TikTokHasTagRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request body"})
		return
	}

	me, _ := c.Get("me")
	user, _ := me.(db.User)
	q := database.NewQuery()

	apifyUrl := fmt.Sprintf("%s%s", config.C.ApiFyProfileUrl, config.C.ApifyApiKey)
	client := resty.New()

	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(req).
		Post(apifyUrl)

	if err != nil {
		fmt.Println("Error calling Apify API:", err)
		c.JSON(500, gin.H{"error": "Failed to call Apify API"})
		return
	}

	fmt.Println("Response Body:", resp.String())

	var videos []map[string]interface{}
	if err := json.Unmarshal([]byte(resp.String()), &videos); err != nil {
		c.JSON(500, gin.H{"error": "Failed to parse Apify response"})
		return
	}

	for _, v := range videos {
		videoID := v["id"].(string)
		createUnix := int64(utils.GetFloat(v["createTime"]))
		createISO := utils.ParseISOTime(v["createTimeISO"].(string))
		expiresAt := time.Now().Add(7 * 24 * time.Hour)

		author := v["authorMeta"].(map[string]interface{})
		music := v["musicMeta"].(map[string]interface{})

		// insert video author if not exists
		if err := q.CreateTKVideoAuthor(c, db.CreateTKVideoAuthorParams{
			ID:                utils.GetString(author, "id"),
			Username:          utils.StringToPGText(utils.GetString(author, "name")),
			Nickname:          utils.StringToPGText(utils.GetString(author, "nickName")),
			ProfileUrl:        utils.StringToPGText(utils.GetString(author, "profileUrl")),
			AvatarUrl:         utils.StringToPGText(utils.GetString(author, "avatar")),
			OriginalAvatarUrl: utils.StringToPGText(utils.GetString(author, "originalAvatarUrl")),
			Signature:         utils.StringToPGText(utils.GetString(author, "signature")),
			BioLink:           utils.StringToPGText(utils.GetString(author, "bioLink")),
			Verified:          utils.PgBool(utils.GetBool(author, "verified")),
			PrivateAccount:    utils.PgBool(utils.GetBool(author, "privateAccount")),
			Region:            utils.StringToPGText(utils.GetString(author, "region")),
			Fans:              utils.PgInt4(int(utils.GetFloat(author["fans"]))),
			Heart:             utils.PgInt8(int64(utils.GetFloat(author["heart"]))),
			Following:         utils.PgInt4(int(utils.GetFloat(author["following"]))),
			Friends:           utils.PgInt4(int(utils.GetFloat(author["friends"]))),
			VideoCount:        utils.PgInt4(int(utils.GetFloat(author["video"]))),
			DiggCount:         utils.PgInt4(0),
		}); err != nil {
			fmt.Println("Insert video meta failed1:", err)
		}

		// insert video music if not exists
		// insert video music if not exists
		if err = q.CreateTKVideoMusic(c, db.CreateTKVideoMusicParams{
			ID:               utils.GetString(music, "musicId"),
			Name:             utils.StringToPGText(utils.GetString(music, "musicName")),
			Author:           utils.StringToPGText(utils.GetString(music, "musicAuthor")),
			IsOriginal:       utils.PgBool(utils.GetBool(music, "musicOriginal")),
			PlayUrl:          utils.StringToPGText(utils.GetString(music, "playUrl")),
			CoverUrl:         utils.StringToPGText(utils.GetString(music, "coverMediumUrl")),
			OriginalCoverUrl: utils.StringToPGText(utils.GetString(music, "originalCoverMediumUrl")),
		}); err != nil {
			fmt.Println("Insert video meta failed2:", err)
		}

		if err = q.CreateTKVideoWithHashTags(c, db.CreateTKVideoWithHashTagsParams{
			ID:                 videoID,
			UserID:             int32(user.ID),
			AuthorID:           utils.GetString(author, "id"),
			MusicID:            utils.StringToPGText(utils.GetString(music, "musicId")),
			Text:               utils.StringToPGText(utils.GetString(v, "text")),
			TextLanguage:       utils.StringToPGText(utils.GetString(v, "textLanguage")),
			VideoUrl:           utils.StringToPGText(utils.GetString(v, "webVideoUrl")),
			CreateTime:         utils.PgTimestamp(time.Unix(createUnix, 0)),
			CreateTimeIso:      utils.PgTimestamp(createISO),
			DiggCount:          utils.PgInt4(int(utils.GetFloat(v["diggCount"]))),
			ShareCount:         utils.PgInt4(int(utils.GetFloat(v["shareCount"]))),
			PlayCount:          utils.PgInt8(int64(utils.GetFloat(v["playCount"]))),
			CollectCount:       utils.PgInt4(int(utils.GetFloat(v["collectCount"]))),
			CommentCount:       utils.PgInt4(int(utils.GetFloat(v["commentCount"]))),
			IsAd:               utils.PgBool(utils.GetBool(v, "isAd")),
			IsSlideshow:        utils.PgBool(utils.GetBool(v, "isSlideshow")),
			IsPinned:           utils.PgBool(utils.GetBool(v, "isPinned")),
			IsSponsored:        utils.PgBool(utils.GetBool(v, "isSponsored")),
			Input:              utils.StringToPGText(utils.GetString(v, "input")),
			SearchHashtag:      utils.StringToPGText(utils.GetString(v, "searchHashtag")),
			SearchHashtagViews: utils.PgInt8(int64(utils.GetFloat(v["searchHashtag"].(map[string]interface{})["views"]))),
			ExpiresAt:          utils.PgTimestamp(expiresAt),
		}); err != nil {
			fmt.Println("Insert video meta failed3:", err)
		}

		for _, h := range v["hashtags"].([]interface{}) {
			tag := h.(map[string]interface{})["name"].(string)
			_ = q.CreateTKVideoHashtagsAndMentions(c, db.CreateTKVideoHashtagsAndMentionsParams{
				VideoID: videoID,
				Hashtag: tag,
			})
		}

		for _, s := range v["effectStickers"].([]interface{}) {
			sticker := s.(map[string]interface{})
			_ = q.CreateTKVideoEffectSticker(c, db.CreateTKVideoEffectStickerParams{
				VideoID:     videoID,
				StickerID:   sticker["ID"].(string),
				StickerName: utils.StringToPGText(sticker["name"].(string)),
			})
		}
	}

	c.JSON(200, gin.H{
		"message": "Video data saved successfully",
		"data":    videos,
	})
}

func (tk *TKVideoController) Destroy(c *gin.Context) {
	panic("not implemented") // TODO: Implement
}

func (tk *TKVideoController) Update(c *gin.Context) {
	panic("not implemented") // TODO: Implement
}

func (tk *TKVideoController) Get(c *gin.Context) {
	panic("not implemented") // TODO: Implement
}

func (tk *TKVideoController) GetPaged(c *gin.Context) {
	me, _ := c.Get("me")
	user, ok := me.(db.User)
	if !ok {
		c.JSON(401, gin.H{"error": "unauthorized"})
		return
	}

	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "10")

	page, _ := strconv.Atoi(pageStr)
	pageSize, _ := strconv.Atoi(pageSizeStr)
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	q := database.NewQuery()
	videos, err := q.ListTKVideoWithAuthorAndMusic(c, db.ListTKVideoWithAuthorAndMusicParams{
		UserID: int32(user.ID),
		Limit:  int32(pageSize),
		Offset: int32(offset),
	})
	if err != nil {
		c.JSON(500, gin.H{"error": "failed to get videos"})
		return
	}

	c.JSON(200, gin.H{
		"page":     page,
		"pageSize": pageSize,
		"data":     videos,
	})
}
