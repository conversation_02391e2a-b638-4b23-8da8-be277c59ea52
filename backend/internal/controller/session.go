package controller

import (
	"log"
	"net/http"
	"server/internal/database"
	"server/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

type SessionController struct{}

func (s *SessionController) RegisterRoutes(rg *gin.RouterGroup) {
	r := rg.Group("/v1")
	r.POST("/sessions", s.Create)
	r.POST("/sessions2", s.ParseJWT)
	// r.PUT("/sessions/:id", s.Update)
	// r.GET("/sessions/:id", s.Get)
	// r.GET("/sessions", s.GetPaged)
}

func (s *SessionController) Create(c *gin.Context) {
	var reqBody struct {
		Name     string `json:"name" binding:"required"`
		Password string `json:"password" binding:"required"`
	}
	if err := c.BindJSON(&reqBody); err != nil {
		c.<PERSON>(400, gin.H{"error": err.Error()})
		return
	}
	q := database.NewQuery()
	user, err := q.GetUser(c, utils.StringToPGText(reqBody.Name))
	if err != nil {
		c.JSON(400, gin.H{"error": err.Error()})
		return
	}
	// err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(reqBody.Password))
	// if err != nil {
	// 	c.JSON(400, gin.H{"error": err.Error()})
	// 	return
	// }

	jwt, err := utils.GenerateJWT(int(user.ID))
	if err != nil {
		log.Println("GenerateJWT fail", err)
		c.String(http.StatusInternalServerError, "wait a minute")
		return
	}

	respBody := gin.H{
		"jwt":    jwt,
		"userId": user.ID,
	}
	c.JSON(200, respBody)
}

// parse req jwt return user id
func (s *SessionController) ParseJWT(c *gin.Context) {
	jwtStr := c.GetHeader("Authorization")
	if jwtStr == "" {
		c.JSON(400, gin.H{"error": "Authorization header is required"})
		return
	}
	token, err := utils.Parse(jwtStr)
	if err != nil {
		c.JSON(400, gin.H{"error": "Invalid JWT"})
		return
	}
	if !token.Valid {
		c.JSON(400, gin.H{"error": "Invalid JWT"})
		return
	}
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		c.JSON(400, gin.H{"error": "Invalid JWT claims"})
		return
	}
	userId, ok := claims["user_id"].(float64)
	if !ok {
		c.JSON(400, gin.H{"error": "Invalid user_id in JWT claims"})
		return
	}
	c.Set("user_id", int(userId))
	c.JSON(200, gin.H{
		"user_id22": int(userId),
	})
}

func (s *SessionController) Destroy(c *gin.Context) {
	panic("not implemented") // TODO: Implement
}

func (s *SessionController) Update(c *gin.Context) {
	panic("not implemented") // TODO: Implement
}

func (s *SessionController) Get(c *gin.Context) {
	panic("not implemented") // TODO: Implement
}

func (s *SessionController) GetPaged(c *gin.Context) {
	panic("not implemented") // TODO: Implement
}
