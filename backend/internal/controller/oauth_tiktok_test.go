package controller

import (
	"bytes"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"testing"
)

func TestTikTok(t *testing.T) {
	url := "https://business-api.tiktok.com/open_api/v1.3/user/info/"

	accessToken := "2fbb01fc800907ae4acc51efabce9c51dcde4069"

	payload := []byte(`{}`)

	req, err := http.NewRequest("GET", url, bytes.NewBuffer(payload))
	if err != nil {
		log.Fatal(err)
	}

	req.Header.Set("Access-Token", accessToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Fatal(err)
	}
	defer resp.Body.Close()

	body, _ := ioutil.ReadAll(resp.Body)

	fmt.Println("Response Status:", resp.Status)
	fmt.Println("Response Body:", string(body))

	t.<PERSON>("Expected 1 to be equal to 1")
}
