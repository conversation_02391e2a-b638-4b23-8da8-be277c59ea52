package controller

import (
	"server/config/db"
	"server/internal/database"
	"server/internal/utils"

	"github.com/gin-gonic/gin"
)

type MeController struct{}

func (m *MeController) RegisterRoutes(rg *gin.RouterGroup) {
	r := rg.Group("/v1")
	r.POST("/sign_up", m.Create)
	r.POST("/author_ids", m.UpdateAuthorIds)
	r.GET("/author_ids", m.GetAuthorIds)
}

func (m *MeController) Create(c *gin.Context) {
	type CreateUserInput struct {
		Email    string `json:"email" binding:"required,email"`
		Name     string `json:"name" binding:"required"`
		Password string `json:"password" binding:"required,min=8"`
		Bio      string `json:"bio"`
	}

	var input CreateUserInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.J<PERSON>(400, gin.H{"error": "Invalid input"})
		return
	}

	q := database.NewQuery()

	// search for existing user by name
	_, err := q.<PERSON>ser(database.DBCtx, utils.StringToPGText(input.Name))
	if err == nil {
		// find user by name
		c.JSON(409, gin.H{"error": "User already exists"})
		return
	}

	c.JSON(200, gin.H{
		"message": "User created successfully",
		// "user":    user,
	})
}

func (m *MeController) Destroy(c *gin.Context) {
	panic("not implemented") // TODO: Implement
}

func (m *MeController) Update(c *gin.Context) {
	panic("not implemented") // TODO: Implement
}

func (m *MeController) Get(c *gin.Context) {
	panic("not implemented") // TODO: Implement
}

func (m *MeController) GetPaged(c *gin.Context) {
	panic("not implemented") // TODO: Implement
}

func (m *MeController) UpdateAuthorIds(c *gin.Context) {
	type UpdateAuthorIdsInput struct {
		AuthorIds []string `json:"authorIds" binding:"required"`
	}
	var input UpdateAuthorIdsInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(400, gin.H{"error": "Invalid input"})
		return
	}

	me, _ := c.Get("me")
	user, _ := me.(db.User)

	// update user
	q := database.NewQuery()
	user, err := q.UpdateUserAuthorUniqueIDs(database.DBCtx, db.UpdateUserAuthorUniqueIDsParams{
		ID:              user.ID,
		AuthorUniqueIds: input.AuthorIds,
	})

	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to update author ids"})
		return
	}
}

func (m *MeController) GetAuthorIds(c *gin.Context) {
	type UpdateAuthorIdsInput struct {
		AuthorIds []string `json:"authorIds" binding:"required"`
	}
	var input UpdateAuthorIdsInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(400, gin.H{"error": "Invalid input"})
		return
	}

	me, _ := c.Get("me")
	user, _ := me.(db.User)

	// update user
	q := database.NewQuery()
	user, err := q.UpdateUserAuthorUniqueIDs(database.DBCtx, db.UpdateUserAuthorUniqueIDsParams{
		ID:              user.ID,
		AuthorUniqueIds: input.AuthorIds,
	})

	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to update author ids"})
		return
	}
}
