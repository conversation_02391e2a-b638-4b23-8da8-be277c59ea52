package controller

import (
	"bytes"
	"context"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"net"
	"net/http"
	"net/url"
	"server/config"
	"server/config/db"
	"server/logger"
	"strings"
	"time"

	"server/internal/database"
	"server/internal/utils"

	"github.com/gin-gonic/gin"
	"golang.org/x/oauth2"
)

type OAuthTikTokController struct{}

func (g *OAuthTikTokController) RegisterRoutes(rg *gin.RouterGroup) {
	r := rg.Group("/v1")
	r.GET("/oauth/tiktok", g.Create)
	r.GET("/auth/tiktok/callback", g.Callback)
	r.GET("/auth/tiktok/user", g.Get)
}

type TiktokUser struct {
	ID        string `json:"open_id"`
	Login     string `json:"username"`
	Email     string `json:"email"` // Note: Email may not always be available
	Name      string `json:"display_name"`
	AvatarURL string `json:"avatar_url"`
}

var tiktokOauthConfig = &oauth2.Config{
	RedirectURL: "http://localhost:8080/api/v1/auth/tiktok/callback",

	ClientID:     config.C.Oauth2.OauthTikTokClient,
	ClientSecret: config.C.Oauth2.OauthTikTokSecret,
	Scopes:       []string{"user:email"},
	Endpoint: oauth2.Endpoint{
		AuthURL:  "https://business-api.tiktok.com/portal/auth",
		TokenURL: "https://business-api.tiktok.com/open_api/v1.3/oauth2/access_token/",
	},
}

const oauthTiktokUrlAPI = "https://open.tiktokapis.com/v2/user/info/"

func generateCodeVerifier() string {
	bytes := make([]byte, 32)
	_, err := rand.Read(bytes)
	if err != nil {
		panic("failed to generate random bytes for code_verifier")
	}
	return base64.RawURLEncoding.EncodeToString(bytes)
}
func generateStateOauthCookieWithTikTok(name string, c *gin.Context) string {
	b := make([]byte, 16)
	rand.Read(b)
	state := base64.URLEncoding.EncodeToString(b)
	domain := config.C.CallbackFrontendUrl[8:]
	c.SetCookie(name, state, expiration, "/", domain, false, true)
	return state
}

func (g *OAuthTikTokController) Create(c *gin.Context) {
	// Get user type from query parameter
	userType := c.Query("user_type")
	if userType == "" {
		userType = "personal" // default to personal
	}

	// Validate user type
	if userType != "personal" && userType != "business" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user type. Must be 'personal' or 'business'"})
		return
	}

	state := generateStateOauthCookieWithTikTok("tiktok_state", c)

	// Store user type in state for callback
	stateWithUserType := fmt.Sprintf("%s:%s", state, userType)
	domain := config.C.CallbackFrontendUrl[8:]
	c.SetCookie("tiktok_user_type", userType, expiration, "/", domain, false, true)

	appID := config.C.Oauth2.OauthTikTokClient
	redirectURI := "https://r27w9q2m-8080.asse.devtunnels.ms/api/v1/auth/tiktok/callback"

	// Use different scopes based on user type
	var authURL string
	if userType == "business" {
		// Business API for e-commerce users
		authURL = fmt.Sprintf(
			"https://business-api.tiktok.com/portal/auth?app_id=%s&state=%s&redirect_uri=%s",
			appID,
			stateWithUserType,
			url.QueryEscape(redirectURI),
		)
	} else {
		// Regular TikTok API for personal users
		authURL = fmt.Sprintf(
			"https://www.tiktok.com/auth/authorize/?client_key=%s&response_type=code&scope=user.info.basic,video.list&redirect_uri=%s&state=%s",
			appID,
			url.QueryEscape(redirectURI),
			stateWithUserType,
		)
	}

	c.JSON(http.StatusOK, gin.H{
		"url":       authURL,
		"user_type": userType,
	})
}

func (g *OAuthTikTokController) Callback(c *gin.Context) {
	code := c.Query("auth_code")
	if code == "" {
		code = c.Query("code")
	}
	state := c.Query("state")
	q := database.NewQuery()

	if code == "" {
		log.Println("Missing auth code")
		c.Redirect(http.StatusTemporaryRedirect, config.C.CallbackFrontendUrl)
		return
	}

	// Extract user type from state or cookie
	var userType string
	if userTypeCookie, err := c.Cookie("tiktok_user_type"); err == nil {
		userType = userTypeCookie
	} else {
		// Fallback: extract from state if it contains user type
		if len(state) > 0 && len(state) > 32 { // state format: "randomstring:usertype"
			parts := strings.Split(state, ":")
			if len(parts) == 2 {
				userType = parts[1]
			}
		}
	}

	// Default to personal if no user type found
	if userType == "" {
		userType = "personal"
	}

	logger.Info("Processing callback at:", time.Now(), "for user type:", userType)

	tokenResponse, _ := ExchangeTikTokCode(c.Request.Context(), code)
	userInfo, err := getUserInfo(tokenResponse.AccessToken)
	if err != nil {
		log.Printf("Failed to fetch TikTok user info: %v", err)
		c.AbortWithStatus(http.StatusInternalServerError)
		return
	}

	_, err = q.GetUserByTikTokID(database.DBCtx, utils.StringToPGText(userInfo.Data.UserID))
	if err != nil {
		// Create new user with user type
		_, err = q.CreateTikTokUser(database.DBCtx, db.CreateTikTokUserParams{
			TiktokID:  utils.StringToPGText(userInfo.Data.UserID),
			Login:     userInfo.Data.DisplayName,
			Email:     utils.StringToPGText(userInfo.Data.Email),
			Name:      utils.StringToPGText(userInfo.Data.DisplayName),
			AvatarUrl: utils.StringToPGText(userInfo.Data.AvatarURL),
			Token:     utils.StringToPGText(c.Query("code")),
			UserType:  utils.StringToPGText(userType),
		})
	} else {
		// Update existing user with user type
		_, err = q.UpdateUserByTikTokID(database.DBCtx, db.UpdateUserByTikTokIDParams{
			TiktokID:  utils.StringToPGText(userInfo.Data.UserID),
			Login:     userInfo.Data.DisplayName,
			Email:     utils.StringToPGText(userInfo.Data.Email),
			Name:      utils.StringToPGText(userInfo.Data.DisplayName),
			AvatarUrl: utils.StringToPGText(userInfo.Data.AvatarURL),
			Token:     utils.StringToPGText(c.Query("code")),
		})
	}
	if err != nil {
		log.Printf("Failed to insert TikTok user: %v", err)
	}

	logger.Info("Successfully exchanged token:", tokenResponse.OpenID)

	domain := config.C.CallbackFrontendUrl[8:]

	c.SetCookie("tiktok_access_token", tokenResponse.AccessToken, expiration, "/", domain, false, false)
	c.SetCookie("tiktok_open_id", tokenResponse.OpenID, expiration, "/", domain, false, false)
	c.SetCookie("tiktok_state", state, expiration, "/", domain, false, false)
	c.SetCookie("tiktok_user_type", userType, expiration, "/", domain, false, false)

	c.Redirect(http.StatusTemporaryRedirect, fmt.Sprintf(
		"%s/auth/tiktok/finalize?state=%s&code=%s&user_type=%s",
		config.C.CallbackFrontendUrl, state, c.Query("code"), userType,
	))
}

func (g *OAuthTikTokController) Destroy(c *gin.Context) {
	panic("not implemented")
}

func (g *OAuthTikTokController) Update(c *gin.Context) {
	panic("not implemented")
}

func (g *OAuthTikTokController) GetPaged(c *gin.Context) {
	panic("not implemented")
}

func (g *OAuthTikTokController) Get(c *gin.Context) {
	me, _ := c.Get("me")
	user := me.(db.User)
	// code, err := c.Cookie("tiktok_code")
	// if err != nil {
	// 	c.JSON(http.StatusUnauthorized, gin.H{"error": "Missing Tiktok_code"})
	// 	return
	// }
	// fmt.Println("Received code:", code)
	q := database.NewQuery()
	user, err := q.GetUserFromCode(database.DBCtx, user.Token)
	if err != nil {
		fmt.Println("Error getting user from code:", err)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"user": user})

}

func ExchangeTikTokCode(ctx context.Context, code string) (*TikTokTokenResponse, error) {

	requestBody := map[string]interface{}{
		"app_id":     config.C.Oauth2.OauthTikTokClient,
		"secret":     config.C.Oauth2.OauthTikTokSecret,
		"auth_code":  code,
		"grant_type": "authorization_code",
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("marshal request body: %w", err)
	}

	logger.Debug("Exchanging TikTok Business API code at:", time.Now())
	logger.Debug("Request body:", string(jsonData))

	req, err := http.NewRequestWithContext(ctx, http.MethodPost,
		"https://business-api.tiktok.com/open_api/v1.3/oauth2/access_token/",
		bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Cache-Control", "no-cache")

	client := &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			DialContext: (&net.Dialer{
				Timeout:   10 * time.Second,
				KeepAlive: 30 * time.Second,
			}).DialContext,
			TLSHandshakeTimeout:   10 * time.Second,
			ResponseHeaderTimeout: 20 * time.Second,
			ExpectContinueTimeout: 1 * time.Second,
		},
	}

	var resp *http.Response
	var lastErr error

	for i := 0; i < 3; i++ {
		logger.Debug("Attempt", i+1, "of 3")

		resp, lastErr = client.Do(req)
		if lastErr == nil {
			break
		}

		logger.Warn("Request failed, attempt", i+1, "error:", lastErr)

		if i < 2 {
			time.Sleep(time.Duration(i+1) * time.Second)
		}
	}

	if lastErr != nil {
		return nil, fmt.Errorf("request failed after 3 attempts: %w", lastErr)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read body: %w", err)
	}

	logger.Debug("Response status:", resp.StatusCode)
	logger.Debug("Response body:", string(body))

	// 修正的Business API响应结构体
	var result struct {
		Code      int    `json:"code"`
		Message   string `json:"message"`
		RequestID string `json:"request_id"`
		Data      struct {
			AccessToken   string  `json:"access_token"`
			RefreshToken  string  `json:"refresh_token"`
			AdvertiserIDs []int64 `json:"advertiser_ids"` // 数组类型
			Scope         []int64 `json:"scope"`          // 数组类型，不是字符串
			ExpiresIn     int     `json:"expires_in"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("unmarshal response: %w body: %s", err, string(body))
	}

	// 检查API响应码
	if result.Code != 0 {
		return nil, fmt.Errorf("TikTok Business API error (code %d): %s (request_id: %s)",
			result.Code, result.Message, result.RequestID)
	}

	if result.Data.AccessToken == "" {
		return nil, fmt.Errorf("empty access_token, raw body: %s", string(body))
	}

	// 将scope数组转换为字符串（如果需要）
	scopeStr := ""
	if len(result.Data.Scope) > 0 {
		scopeBytes, _ := json.Marshal(result.Data.Scope)
		scopeStr = string(scopeBytes)
	}

	return &TikTokTokenResponse{
		AccessToken: result.Data.AccessToken,
		OpenID:      "",
		Scope:       scopeStr,
	}, nil
}

type TikTokTokenResponse struct {
	AccessToken   string  `json:"access_token"`
	RefreshToken  string  `json:"refresh_token"`
	OpenID        string  `json:"open_id"`        // Business API中可能没有这个字段
	Scope         string  `json:"scope"`          // 或者改为 []int64 如果你想保持数组格式
	AdvertiserIDs []int64 `json:"advertiser_ids"` // Business API特有字段
	ExpiresIn     int     `json:"expires_in"`
}

func getUserInfo(accessToken string) (*TikTokBusinessUserInfo, error) {
	url := "https://business-api.tiktok.com/open_api/v1.3/user/info/"

	payload := []byte(`{}`)

	req, err := http.NewRequest("GET", url, bytes.NewBuffer(payload))
	if err != nil {
		log.Fatal(err)
	}

	// 设置必须的 Header
	req.Header.Set("Access-Token", accessToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Fatal(err)
	}
	defer resp.Body.Close()

	body, _ := ioutil.ReadAll(resp.Body)
	var userInfo TikTokBusinessUserInfo
	if err := json.Unmarshal(body, &userInfo); err != nil {
		return nil, err
	}
	logger.Info("UserInfo", userInfo)
	return &userInfo, nil

}

type TikTokBusinessUserInfo struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		DisplayName string `json:"display_name"`
		AvatarURL   string `json:"avatar_url"`
		Email       string `json:"email"`
		UserID      string `json:"core_user_id"`
	} `json:"data"`
}
