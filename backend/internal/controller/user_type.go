package controller

import (
	"net/http"
	"server/config/db"
	"server/internal/database"
	"server/internal/utils"
	"server/types"

	"github.com/gin-gonic/gin"
)

type UserTypeController struct{}

func (ut *UserTypeController) RegisterRoutes(rg *gin.RouterGroup) {
	r := rg.Group("/v1/user-type")
	r.GET("/permissions", ut.GetPermissions)
	r.PUT("/update", ut.UpdateUserType)
	r.GET("/config", ut.GetDashboardConfig)
}

// GetPermissions returns the permissions for the current user's type
func (ut *UserTypeController) GetPermissions(c *gin.Context) {
	me, exists := c.Get("me")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	user := me.(db.User)
	userTypeStr := utils.PGTextToString(user.UserType)

	// Default to personal if user type is empty
	if userTypeStr == "" {
		userTypeStr = "personal"
	}

	userType := types.UserType(userTypeStr)
	if !userType.IsValid() {
		userType = types.UserTypePersonal
	}

	permissions := types.GetPermissions(userType)

	c.JSON(http.StatusOK, types.UserTypeResponse{
		UserType:    userType,
		Permissions: permissions,
	})
}

// UpdateUserType updates the user's type
func (ut *UserTypeController) UpdateUserType(c *gin.Context) {
	me, exists := c.Get("me")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req types.UserTypeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	if !req.UserType.IsValid() {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user type"})
		return
	}

	user := me.(db.User)
	q := database.NewQuery()

	updatedUser, err := q.UpdateUserType(database.DBCtx, db.UpdateUserTypeParams{
		ID:       user.ID,
		UserType: utils.StringToPGText(req.UserType.String()),
	})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user type"})
		return
	}

	permissions := types.GetPermissions(req.UserType)

	c.JSON(http.StatusOK, gin.H{
		"message":     "User type updated successfully",
		"user":        updatedUser,
		"user_type":   req.UserType,
		"permissions": permissions,
	})
}

// GetDashboardConfig returns the dashboard configuration for the current user's type
func (ut *UserTypeController) GetDashboardConfig(c *gin.Context) {
	me, exists := c.Get("me")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	user := me.(db.User)
	userTypeStr := utils.PGTextToString(user.UserType)

	// Default to personal if user type is empty
	if userTypeStr == "" {
		userTypeStr = "personal"
	}

	userType := types.UserType(userTypeStr)
	if !userType.IsValid() {
		userType = types.UserTypePersonal
	}

	config := types.GetDashboardConfig(userType)

	c.JSON(http.StatusOK, gin.H{
		"user_type": userType,
		"config":    config,
	})
}

// Implement required Controller interface methods
func (ut *UserTypeController) Create(c *gin.Context) {
	// This method is used for UpdateUserType functionality
	ut.UpdateUserType(c)
}

func (ut *UserTypeController) Destroy(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented"})
}

func (ut *UserTypeController) Update(c *gin.Context) {
	ut.UpdateUserType(c)
}

func (ut *UserTypeController) Get(c *gin.Context) {
	ut.GetPermissions(c)
}

func (ut *UserTypeController) GetPaged(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented"})
}
