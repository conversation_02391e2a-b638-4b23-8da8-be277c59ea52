package controller

import (
	"encoding/json"
	"fmt"
	"server/config"
	"server/config/db"
	"server/internal/database"
	"server/internal/utils"
	"server/types"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
)

type TKCommentController struct{}

func (tk *TKCommentController) RegisterRoutes(rg *gin.RouterGroup) {
	r := rg.Group("/v1")
	r.POST("/tk_comments", tk.Create)
	r.GET("/tk_comments/:id", tk.Get)
	r.GET("/tk_comments", tk.GetPaged)
}

func (tk *TKCommentController) Create(c *gin.Context) {
	var TikTokCommentRequest types.TikTokCommentRequest

	if err := c.ShouldBindJSON(&TikTokCommentRequest); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request body"})
		return
	}
	cleanedURLs := []string{}
	for _, url := range TikTokCommentRequest.PostURLs {
		if idx := strings.Index(url, "?"); idx != -1 {
			url = url[:idx]
		}
		cleanedURLs = append(cleanedURLs, url)
	}
	TikTokCommentRequest.PostURLs = cleanedURLs

	me, _ := c.Get("me")
	user, _ := me.(db.User)
	q := database.NewQuery()

	// http request to get a TKComment by ID
	client := resty.New()
	apifyUrl := fmt.Sprintf("%s%s", config.C.ApiFyCommentUrl, config.C.ApifyApiKey)

	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(TikTokCommentRequest).
		Post(apifyUrl)

	if err != nil {
		panic(err)
	}

	fmt.Println("Response Body:", resp.String())

	// Here you would typically fetch the TKComment from the database using the ID
	var comments []map[string]interface{}
	err = json.Unmarshal([]byte(resp.String()), &comments)
	if err != nil {
		c.JSON(500, gin.H{"error": "Invalid JSON response from API"})
		return
	}

	now := time.Now()
	expiresAt := now.Add(7 * 24 * time.Hour)

	for _, comment := range comments {
		// Defensive extraction from map
		cid := comment["cid"].(string)
		text := comment["text"].(string)
		videoURL := comment["videoWebUrl"].(string)
		uid := comment["uid"].(string)
		uniqueId := comment["uniqueId"].(string)
		avatar := comment["avatarThumbnail"].(string)
		digg := int(utils.GetFloat(comment["diggCount"]))
		liked := comment["likedByAuthor"].(bool)
		pinned := comment["pinnedByAuthor"].(bool)
		replyTotal := int(utils.GetFloat(comment["replyCommentTotal"]))
		createTime := int64(utils.GetFloat(comment["createTime"]))
		commentTime := time.Unix(createTime, 0)

		err := q.CreateTKComment(database.DBCtx, db.CreateTKCommentParams{
			UserID:            int32(user.ID),
			VideoUrl:          videoURL,
			CommentID:         cid,
			CommentText:       text,
			AuthorUid:         utils.StringToPGText(uid),
			AuthorUniqueID:    utils.StringToPGText(uniqueId),
			AvatarUrl:         utils.StringToPGText(avatar),
			DiggCount:         utils.PgInt4(digg),
			LikedByAuthor:     utils.PgBool(liked),
			PinnedByAuthor:    utils.PgBool(pinned),
			ReplyCommentTotal: utils.PgInt4(replyTotal),
			CommentTime:       utils.PgTimestamp(commentTime),
			ExpiresAt:         utils.PgTimestamp(expiresAt),
		})

		if err != nil {
			fmt.Println("DB insert error:", err)
			// continue instead of return — to insert as many as possible
		}
	}

	c.JSON(200, gin.H{
		"message": "Get TKComment",
		"user":    me,
		"data":    comments,
	})
}

func (tk *TKCommentController) Destroy(c *gin.Context) {
	panic("not implemented") // TODO: Implement
}

func (tk *TKCommentController) Update(c *gin.Context) {
	panic("not implemented") // TODO: Implement
}

func (tk *TKCommentController) Get(c *gin.Context) {

}

func (tk *TKCommentController) GetPaged(c *gin.Context) {
	q := database.NewQuery()

	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "20")
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}
	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	me, _ := c.Get("me")
	user, _ := me.(db.User)

	comments, err := q.ListTKCommentsByUserID(c, db.ListTKCommentsByUserIDParams{
		UserID: int32(user.ID),
		Limit:  int32(pageSize),
		Offset: int32(offset),
	})
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to fetch comments"})
		return
	}
	totalCount, err := q.CountTKCommentsByUserID(c, int32(user.ID))
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to count comments"})
		return
	}
	c.JSON(200, gin.H{
		"total":     totalCount,
		"page":      page,
		"page_size": pageSize,
		"data":      comments,
	})
}
