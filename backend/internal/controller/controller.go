package controller

import "github.com/gin-gonic/gin"

type Controller interface {
	RegisterRoutes(rg *gin.RouterGroup)
	Create(c *gin.Context)
	Destroy(c *gin.Context)
	Update(c *gin.Context)
	Get(c *gin.Context)
	GetPaged(c *gin.Context)
}

type TikTokBasicRequest struct {
	ExcludePinnedPosts            bool     `json:"excludePinnedPosts"`
	ResultsPerPage                int      `json:"resultsPerPage"`
	ScrapeRelatedVideos           bool     `json:"scrapeRelatedVideos"`
	ShouldDownloadAvatars         bool     `json:"shouldDownloadAvatars"`
	ShouldDownloadCovers          bool     `json:"shouldDownloadCovers"`
	ShouldDownloadMusicCovers     bool     `json:"shouldDownloadMusicCovers"`
	ShouldDownloadSlideshowImages bool     `json:"shouldDownloadSlideshowImages"`
	ShouldDownloadSubtitles       bool     `json:"shouldDownloadSubtitles"`
	ShouldDownloadVideos          bool     `json:"shouldDownloadVideos"`
	ProfileScrapeSections         []string `json:"profileScrapeSections"`
	ProfileSorting                string   `json:"profileSorting"`
	SearchSection                 string   `json:"searchSection"`
	MaxProfilesPerQuery           int      `json:"maxProfilesPerQuery"`
}
