package controller

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"server/config"
	"server/config/db"
	"server/internal/database"
	"server/internal/utils"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/github"
)

type OAuthGithubController struct{}

var expiration = int(365 * 24 * time.Hour / time.Second) // one year

// User struct to match GitHub API response
type GithubUser struct {
	ID         int64  `json:"id"`
	Login      string `json:"login"`
	Email      string `json:"email"`
	Name       string `json:"name"`
	Avatar_url string `json:"avatar_url"`
}

// User struct for database
type User struct {
	GithubID  int64     `json:"github_id"`
	Login     string    `json:"login"`
	Email     string    `json:"email"`
	Name      string    `json:"name"`
	CreatedAt time.Time `json:"created_at"`
}

func (g *OAuthGithubController) RegisterRoutes(rg *gin.RouterGroup) {
	r := rg.Group("/v1")
	r.GET("/oauth/github", g.Create)
	r.GET("/auth/github/callback", g.Callback)
	r.GET("/auth/github/user", g.Get)
}

var githubOauthConfig = &oauth2.Config{
	RedirectURL:  "http://localhost:8080/api/v1/auth/github/callback",
	ClientID:     config.C.Oauth2.OauthGitHubClient,
	ClientSecret: config.C.Oauth2.OauthGitHubSecret,
	Scopes:       []string{"user:email"},
	Endpoint:     github.Endpoint,
}

const oauthGithubUrlAPI = "https://api.github.com/user"

func (g *OAuthGithubController) Create(c *gin.Context) {
	// Create oauthState cookie
	oauthState := generateStateOauthCookie("github_state", c)

	// Generate the GitHub OAuth authorization URL
	u := githubOauthConfig.AuthCodeURL(oauthState)
	// c.Redirect(http.StatusTemporaryRedirect, u)
	c.JSON(http.StatusOK, gin.H{
		"url": u,
	})
}

func (g *OAuthGithubController) Callback(c *gin.Context) {
	oauthState, err := c.Cookie("github_state")
	if err != nil || c.Query("state") != oauthState {
		log.Println("invalid oauth github state")
		c.Redirect(http.StatusTemporaryRedirect, "http://localhost:3000")
		return
	}

	data, err := getUserDataFromGithub(c.Query("code"))
	if err != nil {
		log.Printf("Error getting user data: %v", err)
		c.Redirect(http.StatusTemporaryRedirect, "http://localhost:3000")
		return
	}

	fmt.Println("User data received from GitHub:", string(data))

	var githubUser GithubUser
	if err := json.Unmarshal(data, &githubUser); err != nil {
		log.Printf("Error parsing user data: %v", err)
		c.Redirect(http.StatusTemporaryRedirect, "http://localhost:3000")
		return
	}

	q := database.NewQuery()

	_, err = q.GetUserByGithubID(database.DBCtx, utils.StringToPGText(string(githubUser.ID)))
	if err != nil {
		fmt.Println("User not found, creating new user")
		arg := db.CreateGithubUserParams{
			GithubID:  utils.StringToPGText(string(githubUser.ID)),
			Login:     githubUser.Login,
			Email:     utils.StringToPGText(githubUser.Email),
			Name:      utils.StringToPGText(githubUser.Name),
			AvatarUrl: utils.StringToPGText(githubUser.Avatar_url),
			Token:     utils.StringToPGText(c.Query("code")),
		}

		_, err := q.CreateGithubUser(database.DBCtx, arg)
		if err != nil {
			log.Printf("Error storing user: %v", err)
			c.Redirect(http.StatusTemporaryRedirect, "http://localhost:3000")
			return
		}
	} else {
		fmt.Println("User exists, updating info...")

		arg := db.UpdateUserByGithubIDParams{
			GithubID:  utils.StringToPGText(string(githubUser.ID)),
			Login:     githubUser.Login,
			Email:     utils.StringToPGText(githubUser.Email),
			Name:      utils.StringToPGText(githubUser.Name),
			AvatarUrl: utils.StringToPGText(githubUser.Avatar_url),
			Token:     utils.StringToPGText(c.Query("code")),
		}

		_, err := q.UpdateUserByGithubID(database.DBCtx, arg)
		if err != nil {
			log.Printf("Error updating user: %v", err)
			c.Redirect(http.StatusTemporaryRedirect, "http://localhost:3000")
			return
		}
	}

	c.SetCookie("github_code", c.Query("code"), expiration, "/", "localhost", false, true)

	c.Redirect(http.StatusTemporaryRedirect, fmt.Sprintf(
		"http://localhost:3000/auth/github/finalize?code=%s&state=%s",
		c.Query("code"), c.Query("state"),
	))
}

func (g *OAuthGithubController) Destroy(c *gin.Context) {
	panic("not implemented")
}

func (g *OAuthGithubController) Update(c *gin.Context) {
	panic("not implemented")
}

func (g *OAuthGithubController) GetPaged(c *gin.Context) {
	panic("not implemented")
}

func (g *OAuthGithubController) Get(c *gin.Context) {
	code, err := c.Cookie("github_code")
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Missing github_code"})
		return
	}
	fmt.Println("Received code:", code)
	q := database.NewQuery()
	user, err := q.GetUserFromCode(database.DBCtx, utils.StringToPGText(code))
	if err != nil {
		fmt.Println("Error getting user from code:", err)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"user": user})

}

func generateStateOauthCookie(name string, c *gin.Context) string {
	b := make([]byte, 16)
	rand.Read(b)
	state := base64.URLEncoding.EncodeToString(b)
	c.SetCookie(name, state, expiration, "/", "localhost", false, true)
	return state
}

func getUserDataFromGithub(code string) ([]byte, error) {
	// Exchange code for access token
	token, err := githubOauthConfig.Exchange(context.Background(), code)
	if err != nil {
		return nil, fmt.Errorf("code exchange failed: %s", err.Error())
	}

	// Get user info from GitHub
	client := githubOauthConfig.Client(context.Background(), token)
	response, err := client.Get(oauthGithubUrlAPI)
	if err != nil {
		return nil, fmt.Errorf("failed getting user info: %s", err.Error())
	}
	defer response.Body.Close()

	contents, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return nil, fmt.Errorf("failed reading response: %s", err.Error())
	}
	return contents, nil
}
