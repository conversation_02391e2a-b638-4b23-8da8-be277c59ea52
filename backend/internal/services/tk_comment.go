package services

import (
	"encoding/json"
	"fmt"
	"server/config"
	"server/config/db"
	"server/internal/database"
	"server/internal/utils"
	"server/logger"
	"server/types"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
)

type TKCommentService struct {
	Q *db.Queries
}

func NewTKCommentService() *TKCommentService {
	return &TKCommentService{
		Q: database.NewQuery(),
	}
}

func (s *TKCommentService) FetchAndSaveTKComments(user db.User, req types.TikTokCommentRequest) ([]map[string]interface{}, error) {

	cleanedURLs := []string{}
	for _, url := range req.PostURLs {
		if idx := strings.Index(url, "?"); idx != -1 {
			url = url[:idx]
		}
		cleanedURLs = append(cleanedURLs, url)
	}
	req.PostURLs = cleanedURLs

	client := resty.New()
	apifyUrl := fmt.Sprintf("%s%s", config.C.ApiFyCommentUrl, config.C.ApifyApiKey)

	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(req).
		Post(apifyUrl)
	if err != nil {
		return nil, fmt.Errorf("API request error: %w", err)
	}

	var comments []map[string]interface{}
	if err := json.Unmarshal(resp.Body(), &comments); err != nil {
		return nil, fmt.Errorf("Invalid JSON response: %w", err)
	}

	now := time.Now()
	expiresAt := now.Add(7 * 24 * time.Hour)

	for _, comment := range comments {
		cid, _ := comment["cid"].(string)
		text, _ := comment["text"].(string)
		videoURL, _ := comment["videoWebUrl"].(string)
		uid, _ := comment["uid"].(string)
		uniqueId, _ := comment["uniqueId"].(string)
		avatar, _ := comment["avatarThumbnail"].(string)
		digg := int(utils.GetFloat(comment["diggCount"]))
		liked, _ := comment["likedByAuthor"].(bool)
		pinned, _ := comment["pinnedByAuthor"].(bool)
		replyTotal := int(utils.GetFloat(comment["replyCommentTotal"]))
		createTime := int64(utils.GetFloat(comment["createTime"]))
		commentTime := time.Unix(createTime, 0)

		err := s.Q.CreateTKComment(database.DBCtx, db.CreateTKCommentParams{
			UserID:            int32(user.ID),
			VideoUrl:          videoURL,
			CommentID:         cid,
			CommentText:       text,
			AuthorUid:         utils.StringToPGText(uid),
			AuthorUniqueID:    utils.StringToPGText(uniqueId),
			AvatarUrl:         utils.StringToPGText(avatar),
			DiggCount:         utils.PgInt4(digg),
			LikedByAuthor:     utils.PgBool(liked),
			PinnedByAuthor:    utils.PgBool(pinned),
			ReplyCommentTotal: utils.PgInt4(replyTotal),
			CommentTime:       utils.PgTimestamp(commentTime),
			ExpiresAt:         utils.PgTimestamp(expiresAt),
		})
		if err != nil {
			logger.Error("DB insert error:", "err", err)
		}
	}

	return comments, nil
}
