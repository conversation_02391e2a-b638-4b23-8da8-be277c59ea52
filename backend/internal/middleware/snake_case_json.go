package middleware

import (
	"bytes"
	"encoding/json"
	"fmt"
	"server/internal/utils"

	"github.com/gin-gonic/gin"
)

type bodyWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w *bodyWriter) Write(b []byte) (int, error) {
	return w.body.Write(b)
}
func SnakeCaseJSON() gin.HandlerFunc {
	return func(c *gin.Context) {
		writer := &bodyWriter{
			ResponseWriter: c.Writer,
			body:           bytes.NewBufferString(""),
		}
		c.Writer = writer

		c.Next()

		if c.Writer.Status() >= 200 && c.Writer.Status() < 300 &&
			c.Writer.Header().Get("Content-Type") == "application/json; charset=utf-8" {

			raw := writer.body.Bytes()

			var decoded interface{}
			if err := json.Unmarshal(raw, &decoded); err != nil {
				c.Writer.WriteHeaderNow()
				_, _ = c.Writer.Write(raw)
				return
			}

			snake := utils.ConvertMapKeysToSnakeCase(decoded)

			newJSON, err := json.Marshal(snake)
			if err != nil {
				c.Writer.WriteHeaderNow()
				_, _ = c.Writer.Write(raw)
				return
			}
			c.Writer.Header().Set("Content-Length", fmt.Sprintf("%d", len(newJSON)))
			c.Writer.Header().Set("Content-Type", "application/json; charset=utf-8")
			c.Writer.WriteHeaderNow()
			_, _ = c.Writer.Write(newJSON)
		} else {
			c.Writer.WriteHeaderNow()
			_, _ = c.Writer.Write(writer.body.Bytes())
		}
	}
}
