package middleware

import (
	"errors"
	"server/config/db"
	"server/internal/database"
	"server/internal/utils"
	"server/logger"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

func Me(whiteList []string) gin.HandlerFunc {
	logger.Debug("Me middleware called")
	return func(c *gin.Context) {
		path := c.Request.URL.Path
		index := indexOf(whiteList, path)

		if index != -1 {
			c.Next()
			return
		}
		user, err := getMeWithOauthCode(c)
		if err != nil {
			c.JSON(401, gin.H{"error": err.Error()})
			c.Abort()
			return
		}
		c.Set("me", user)
		c.Next()
	}
}

func getMeWithOauthCode(c *gin.Context) (db.User, error) {
	var user db.User

	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		// c.String(401, "missing authorization header")
		return user, errors.New("missing authorization header")
	}

	var code string
	if len(authHeader) > 7 && authHeader[:7] == "Bearer " {
		code = authHeader[7:]
	} else {
		// c.JSON(401, gin.H{"error": "invalid authorization header"})
		return user, errors.New("invalid authorization header")
	}

	if code == "" {
		// c.JSON(401, gin.H{"error": "empty token"})
		return user, errors.New("empty token")
	}
	logger.Info("code: ", "code", code)

	q := database.NewQuery()
	user, err := q.GetUserFromCode(c, utils.StringToPGText(code))
	if err != nil {
		// c.JSON(401, gin.H{"error": "invalid user"})
		return user, errors.New("invalid user")
	}

	logger.Info("User found:", "user", user)

	return user, nil
}

func getMeWithJWT(c *gin.Context) (db.User, error) {
	var user db.User
	auth := c.GetHeader("Authorization")
	if len(auth) < 8 {
		return user, errors.New("unauthorized")
	}
	jwtString := auth[7:]
	t, err := utils.Parse(jwtString)
	if err != nil {
		c.JSON(401, gin.H{"error": "invalid authorization header"})
		return user, errors.New("unauthorized")
	}
	m, ok := t.Claims.(jwt.MapClaims)
	if !ok {
		c.JSON(401, gin.H{"error": "invalid authorization header"})
		return user, errors.New("unauthorized")
	}
	userID, ok := m["user_id"].(float64)
	if !ok {
		c.JSON(401, gin.H{"error": "invalid authorization header"})
		return user, errors.New("unauthorized")

	}
	userIDInt := int64(userID)
	q := database.NewQuery()

	user, err = q.GetUserWithID(c, userIDInt)
	if err != nil {
		c.JSON(401, gin.H{"error": "invalid user"})
		return user, errors.New("unauthorized")
	}
	return user, nil
}

func indexOf(slice []string, s string) int {
	for i, v := range slice {
		if v == s {
			return i
		}
	}
	return -1
}
