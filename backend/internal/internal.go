package internal

import (
	"server/config"
	"server/internal/middleware"
	"server/logger"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func InitRouter(r *gin.Engine) {
	r.Use(func(c *gin.Context) {
		logger.Debug("Request method:", c.Request.Method, config.C.CallbackFrontendUrl)
		c.Next()
	})

	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{config.C.CallbackFrontendUrl},
		AllowMethods:     []string{"GET", "POST", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))

	// r.Use(middleware.SnakeCaseJSON())
	r.Use(middleware.Me([]string{
		"/api/v1/sessions",
		"/api/v1/oauth/github",
		"/api/v1/oauth/tiktok",
		"/api/v1/auth/github/callback",
		"/api/v1/auth/tiktok/callback",
		"/api/v1/auth/github/user",
		// "/api/v1/auth/tiktok/user",
		"/api/v1/sign_up",
		"/ping",
	}))
}
