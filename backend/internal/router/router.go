package router

import (
	"server/internal"
	"server/internal/controller"

	"github.com/gin-gonic/gin"
)

func loadController() []controller.Controller {
	return []controller.Controller{
		&controller.MeController{},
		&controller.SessionController{},
		&controller.TKCommentController{},
		&controller.TKProfileController{},
		&controller.TKVideoController{},
		&controller.OAuthGithubController{},
		&controller.OAuthTikTokController{},
		&controller.AIAgentController{},
		&controller.UserTypeController{},
	}
}

func New() *gin.Engine {

	r := gin.Default()

	internal.InitRouter(r)
	api := r.Group("/api")

	for _, c := range loadController() {
		c.RegisterRoutes(api)
	}
	r.GET("/ping", controller.Ping)
	return r
}
