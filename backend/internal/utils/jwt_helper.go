package utils

import (
	"crypto/rand"
	"io"

	"github.com/golang-jwt/jwt/v5"
)

func GenerateJWT(user_id int) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"user_id": user_id,
	})
	key, err := getHMACKey()

	if err != nil {
		return "", err
	}
	return token.SignedString(key)
}

func GenerateHMACKey() ([]byte, error) {
	key := make([]byte, 64)
	_, err := io.ReadFull(rand.Reader, key)
	if err != nil {
		return nil, err
	}
	return key, nil
}

func getHMACKey() ([]byte, error) {
	// keyPath := "112233"
	// return ioutil.ReadFile(keyPath)
	return []byte("abcdefg"), nil
}

func Parse(jwtStr string) (*jwt.Token, error) {
	key, err := getHMACKey()
	if err != nil {
		return nil, err
	}
	return jwt.Parse(jwtStr, func(token *jwt.Token) (interface{}, error) {
		return key, nil
	})

}
