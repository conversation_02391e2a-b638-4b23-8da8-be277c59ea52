package utils

import (
	"regexp"
	"strings"
)

var matchFirstCap = regexp.MustCompile("(.)([A-Z][a-z]+)")
var matchAllCap = regexp.MustCompile("([a-z0-9])([A-Z])")

func ToSnakeCase(str string) string {
	snake := matchFirstCap.ReplaceAllString(str, "${1}_${2}")
	snake = matchAllCap.ReplaceAllString(snake, "${1}_${2}")
	return strings.ToLower(snake)
}

func ConvertMapKeysToSnakeCase(data interface{}) interface{} {
	switch v := data.(type) {
	case map[string]interface{}:
		newMap := make(map[string]interface{})
		for key, value := range v {
			newKey := ToSnakeCase(key)
			newMap[newKey] = ConvertMapKeysToSnakeCase(value)
		}
		return newMap
	case []interface{}:
		for i, elem := range v {
			v[i] = ConvertMapKeysToSnakeCase(elem)
		}
		return v
	default:
		return v
	}
}
