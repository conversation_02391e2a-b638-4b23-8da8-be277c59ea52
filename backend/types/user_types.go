package types

// UserType represents the type of user in the system
type UserType string

const (
	// UserTypePersonal represents individual TikTok users
	UserTypePersonal UserType = "personal"
	// UserTypeBusiness represents e-commerce/business users
	UserTypeBusiness UserType = "business"
)

// IsValid checks if the user type is valid
func (ut UserType) IsValid() bool {
	switch ut {
	case UserTypePersonal, UserTypeBusiness:
		return true
	default:
		return false
	}
}

// String returns the string representation of UserType
func (ut UserType) String() string {
	return string(ut)
}

// UserPermissions defines what features are available to each user type
type UserPermissions struct {
	// Basic features available to all users
	CanAnalyzeURL        bool `json:"can_analyze_url"`
	CanViewBasicMetrics  bool `json:"can_view_basic_metrics"`
	CanUseAIAnalysis     bool `json:"can_use_ai_analysis"`
	
	// Advanced features for business users
	CanViewCompetitorAnalysis bool `json:"can_view_competitor_analysis"`
	CanViewROIAnalysis       bool `json:"can_view_roi_analysis"`
	CanViewAdvancedMetrics   bool `json:"can_view_advanced_metrics"`
	CanAccessBusinessAPI     bool `json:"can_access_business_api"`
	CanViewAdInsights        bool `json:"can_view_ad_insights"`
	CanExportData           bool `json:"can_export_data"`
}

// GetPermissions returns the permissions for a given user type
func GetPermissions(userType UserType) UserPermissions {
	switch userType {
	case UserTypePersonal:
		return UserPermissions{
			CanAnalyzeURL:             true,
			CanViewBasicMetrics:       true,
			CanUseAIAnalysis:         true,
			CanViewCompetitorAnalysis: false,
			CanViewROIAnalysis:       false,
			CanViewAdvancedMetrics:   false,
			CanAccessBusinessAPI:     false,
			CanViewAdInsights:        false,
			CanExportData:           false,
		}
	case UserTypeBusiness:
		return UserPermissions{
			CanAnalyzeURL:             true,
			CanViewBasicMetrics:       true,
			CanUseAIAnalysis:         true,
			CanViewCompetitorAnalysis: true,
			CanViewROIAnalysis:       true,
			CanViewAdvancedMetrics:   true,
			CanAccessBusinessAPI:     true,
			CanViewAdInsights:        true,
			CanExportData:           true,
		}
	default:
		// Default to personal user permissions
		return GetPermissions(UserTypePersonal)
	}
}

// UserTypeRequest represents the request structure for user type selection
type UserTypeRequest struct {
	UserType UserType `json:"user_type" binding:"required"`
}

// UserTypeResponse represents the response structure for user type operations
type UserTypeResponse struct {
	UserType    UserType        `json:"user_type"`
	Permissions UserPermissions `json:"permissions"`
}

// DashboardConfig defines the dashboard configuration for each user type
type DashboardConfig struct {
	ShowCompetitorAnalysis bool     `json:"show_competitor_analysis"`
	ShowROIMetrics        bool     `json:"show_roi_metrics"`
	ShowAdvancedCharts    bool     `json:"show_advanced_charts"`
	AvailableFeatures     []string `json:"available_features"`
	MaxAnalysisRequests   int      `json:"max_analysis_requests"`
}

// GetDashboardConfig returns the dashboard configuration for a given user type
func GetDashboardConfig(userType UserType) DashboardConfig {
	switch userType {
	case UserTypePersonal:
		return DashboardConfig{
			ShowCompetitorAnalysis: false,
			ShowROIMetrics:        false,
			ShowAdvancedCharts:    false,
			AvailableFeatures: []string{
				"url_analysis",
				"basic_metrics",
				"ai_content_suggestions",
				"engagement_tracking",
			},
			MaxAnalysisRequests: 50, // per day
		}
	case UserTypeBusiness:
		return DashboardConfig{
			ShowCompetitorAnalysis: true,
			ShowROIMetrics:        true,
			ShowAdvancedCharts:    true,
			AvailableFeatures: []string{
				"url_analysis",
				"basic_metrics",
				"ai_content_suggestions",
				"engagement_tracking",
				"competitor_analysis",
				"roi_analysis",
				"advanced_metrics",
				"business_api_access",
				"ad_insights",
				"data_export",
			},
			MaxAnalysisRequests: 500, // per day
		}
	default:
		return GetDashboardConfig(UserTypePersonal)
	}
}
