package types

import "time"

type ApifyVideoHashTagResponse struct {
	AuthorMeta struct {
		Avatar            string      `json:"avatar"`
		BioLink           interface{} `json:"bio_link"`
		Digg              int         `json:"digg"`
		Fans              int         `json:"fans"`
		Following         int         `json:"following"`
		Friends           int         `json:"friends"`
		Heart             int         `json:"heart"`
		ID                string      `json:"id"`
		Name              string      `json:"name"`
		NickName          string      `json:"nick_name"`
		OriginalAvatarURL string      `json:"original_avatar_url"`
		PrivateAccount    bool        `json:"private_account"`
		ProfileURL        string      `json:"profile_url"`
		Signature         string      `json:"signature"`
		Verified          bool        `json:"verified"`
		Video             int         `json:"video"`
	} `json:"author_meta"`
	CollectCount     int           `json:"collect_count"`
	CommentCount     int           `json:"comment_count"`
	CreateTime       int           `json:"create_time"`
	CreateTimeISO    time.Time     `json:"create_time_iso"`
	DetailedMentions []interface{} `json:"detailed_mentions"`
	DiggCount        int           `json:"digg_count"`
	EffectStickers   []struct {
		ID           string `json:"id"`
		Name         string `json:"name"`
		StickerStats struct {
			UseCount int `json:"use_count"`
		} `json:"sticker_stats"`
	} `json:"effect_stickers"`
	Hashtags []struct {
		Name string `json:"name"`
	} `json:"hashtags"`
	ID          string        `json:"id"`
	Input       string        `json:"input"`
	IsAd        bool          `json:"is_ad"`
	IsPinned    bool          `json:"is_pinned"`
	IsSlideshow bool          `json:"is_slideshow"`
	IsSponsored bool          `json:"is_sponsored"`
	MediaUrls   []interface{} `json:"media_urls"`
	Mentions    []interface{} `json:"mentions"`
	MusicMeta   struct {
		CoverMediumURL         string `json:"cover_medium_url"`
		MusicAuthor            string `json:"music_author"`
		MusicID                string `json:"music_id"`
		MusicName              string `json:"music_name"`
		MusicOriginal          bool   `json:"music_original"`
		OriginalCoverMediumURL string `json:"original_cover_medium_url"`
		PlayURL                string `json:"play_url"`
	} `json:"music_meta"`
	PlayCount     int `json:"play_count"`
	SearchHashtag struct {
		Name  string `json:"name"`
		Views int64  `json:"views"`
	} `json:"search_hashtag"`
	ShareCount   int    `json:"share_count"`
	Text         string `json:"text"`
	TextLanguage string `json:"text_language"`
	VideoMeta    struct {
		CoverURL         string `json:"cover_url"`
		Definition       string `json:"definition"`
		Duration         int    `json:"duration"`
		Format           string `json:"format"`
		Height           int    `json:"height"`
		OriginalCoverURL string `json:"original_cover_url"`
		SubtitleLinks    []struct {
			DownloadLink        string `json:"download_link"`
			Language            string `json:"language"`
			Source              string `json:"source"`
			SourceUnabbreviated string `json:"source_unabbreviated"`
			TiktokLink          string `json:"tiktok_link"`
			Version             string `json:"version"`
		} `json:"subtitle_links"`
		Width int `json:"width"`
	} `json:"video_meta"`
	WebVideoURL string `json:"web_video_url"`
}
