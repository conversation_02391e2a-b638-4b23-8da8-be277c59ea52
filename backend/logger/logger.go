package logger

import (
	"log/slog"
	"os"

	"github.com/charmbracelet/log"
)

func init() {
	logger := log.NewWithOptions(os.Stderr, log.Options{
		Level: log.DebugLevel,
	})
	log.SetDefault(logger)

	slog.SetDefault(slog.New(logger))
}

func Debug(msg string, args ...any) {
	log.Debug(msg, args...)
}

func Error(msg string, args ...any) {
	log.Error(msg, args...)
}

func Info(msg string, args ...any) {
	log.Info(msg, args...)
}

func Warn(msg string, args ...any) {
	log.Warn(msg, args...)
}

func Fatal(msg string, args ...any) {
	log.Fatal(msg, args...)
}
