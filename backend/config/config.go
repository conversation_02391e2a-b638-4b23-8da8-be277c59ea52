package config

import (
	"fmt"

	"github.com/caarlos0/env/v6"
)

type Config struct {
	Postgres            Postgres `envPrefix:"DATABASE_"`           // PG database configuration
	ApiFyCommentUrl     string   `env:"APIFY_TIKTOK_COMMENTS_URL"` // Apify API URL
	ApifyApiKey         string   `env:"APIFY_API_KEY"`
	ApiFyProfileUrl     string   `env:"APIFY_TIKTOK_PROFILE_URL"` // Apify API URL for profiles
	Oauth2              Oauth2   `envPrefix:"OAUTH2_"`
	AiAnalytics         string   `env:"AI_ANALYTICS_URL"`
	CallbackFrontendUrl string   `env:"CALLBACK_FRONTEND_URL"`
}

type Oauth2 struct {
	OauthGitHubClient string `env:"GITHUB_CLIENT_ID"`     // GitHub OAuth client ID
	OauthGitHubSecret string `env:"GITHUB_CLIENT_SECRET"` // GitHub OAuth client secret
	OauthTikTokClient string `env:"TIKTOK_CLIENT_ID"`     // Tiktok OAuth client ID
	OauthTikTokSecret string `env:"TIKTOK_CLIENT_SECRET"` // Tiktok OAuth client secret
}

type Postgres struct {
	User     string `env:"USER"`
	Password string `env:"PASSWORD"`
	DBName   string `env:"DB_NAME"`
	Port     string `env:"PORT"`
	Host     string `env:"HOST"`
}

var C Config

func init() {
	if err := env.Parse(&C); err != nil {
		fmt.Printf("%+v\n", err)
	}

	fmt.Printf("%+v\n", C)
}
