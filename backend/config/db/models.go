// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.25.0

package db

import (
	"github.com/jackc/pgx/v5/pgtype"
)

type AiSortedComment struct {
	ID                    int32              `json:"id"`
	UserID                int32              `json:"user_id"`
	VideoUrl              string             `json:"video_url"`
	Text                  pgtype.Text        `json:"text"`
	IntentScore           pgtype.Int4        `json:"intent_score"`
	Labels                []string           `json:"labels"`
	Urls                  []string           `json:"urls"`
	FinalScore            pgtype.Float8      `json:"final_score"`
	ScenarioReinforcement pgtype.Text        `json:"scenario_reinforcement"`
	CreatedAt             pgtype.Timestamptz `json:"created_at"`
}

type AiSortedKoc struct {
	ID                    int32              `json:"id"`
	UserID                int32              `json:"user_id"`
	VideoUrl              string             `json:"video_url"`
	IntentScore           pgtype.Int4        `json:"intent_score"`
	Labels                []string           `json:"labels"`
	Urls                  []string           `json:"urls"`
	FinalScore            pgtype.Float8      `json:"final_score"`
	ScenarioReinforcement pgtype.Text        `json:"scenario_reinforcement"`
	CreatedAt             pgtype.Timestamptz `json:"created_at"`
}

type TiktokAuthor struct {
	ID                string           `json:"id"`
	Username          pgtype.Text      `json:"username"`
	Nickname          pgtype.Text      `json:"nickname"`
	ProfileUrl        pgtype.Text      `json:"profile_url"`
	AvatarUrl         pgtype.Text      `json:"avatar_url"`
	OriginalAvatarUrl pgtype.Text      `json:"original_avatar_url"`
	Signature         pgtype.Text      `json:"signature"`
	BioLink           pgtype.Text      `json:"bio_link"`
	Verified          pgtype.Bool      `json:"verified"`
	Region            pgtype.Text      `json:"region"`
	PrivateAccount    pgtype.Bool      `json:"private_account"`
	Fans              pgtype.Int4      `json:"fans"`
	Heart             pgtype.Int8      `json:"heart"`
	Following         pgtype.Int4      `json:"following"`
	Friends           pgtype.Int4      `json:"friends"`
	VideoCount        pgtype.Int4      `json:"video_count"`
	DiggCount         pgtype.Int4      `json:"digg_count"`
	CreatedAt         pgtype.Timestamp `json:"created_at"`
}

type TiktokComment struct {
	ID                int32            `json:"id"`
	UserID            int32            `json:"user_id"`
	VideoUrl          string           `json:"video_url"`
	CommentID         string           `json:"comment_id"`
	CommentText       string           `json:"comment_text"`
	AuthorUid         pgtype.Text      `json:"author_uid"`
	AuthorUniqueID    pgtype.Text      `json:"author_unique_id"`
	AvatarUrl         pgtype.Text      `json:"avatar_url"`
	DiggCount         pgtype.Int4      `json:"digg_count"`
	LikedByAuthor     pgtype.Bool      `json:"liked_by_author"`
	PinnedByAuthor    pgtype.Bool      `json:"pinned_by_author"`
	ReplyCommentTotal pgtype.Int4      `json:"reply_comment_total"`
	CreatedAt         pgtype.Timestamp `json:"created_at"`
	CommentTime       pgtype.Timestamp `json:"comment_time"`
	ExpiresAt         pgtype.Timestamp `json:"expires_at"`
}

type TiktokMusic struct {
	ID               string      `json:"id"`
	Name             pgtype.Text `json:"name"`
	Author           pgtype.Text `json:"author"`
	IsOriginal       pgtype.Bool `json:"is_original"`
	OriginalCoverUrl pgtype.Text `json:"original_cover_url"`
	PlayUrl          pgtype.Text `json:"play_url"`
	CoverUrl         pgtype.Text `json:"cover_url"`
}

type TiktokVideo struct {
	ID           string           `json:"id"`
	Text         pgtype.Text      `json:"text"`
	TextLanguage pgtype.Text      `json:"text_language"`
	AuthorID     string           `json:"author_id"`
	MusicID      pgtype.Text      `json:"music_id"`
	UserID       int32            `json:"user_id"`
	VideoUrl     pgtype.Text      `json:"video_url"`
	CoverUrl     pgtype.Text      `json:"cover_url"`
	Width        pgtype.Int4      `json:"width"`
	Height       pgtype.Int4      `json:"height"`
	Duration     pgtype.Int4      `json:"duration"`
	Definition   pgtype.Text      `json:"definition"`
	CreateTime   pgtype.Timestamp `json:"create_time"`
	CommentCount pgtype.Int4      `json:"comment_count"`
	DiggCount    pgtype.Int4      `json:"digg_count"`
	ShareCount   pgtype.Int4      `json:"share_count"`
	PlayCount    pgtype.Int8      `json:"play_count"`
	CollectCount pgtype.Int4      `json:"collect_count"`
	IsAd         pgtype.Bool      `json:"is_ad"`
	IsPinned     pgtype.Bool      `json:"is_pinned"`
	IsSponsored  pgtype.Bool      `json:"is_sponsored"`
	IsSlideshow  pgtype.Bool      `json:"is_slideshow"`
	ExpiresAt    pgtype.Timestamp `json:"expires_at"`
	CreatedAt    pgtype.Timestamp `json:"created_at"`
}

type TiktokVideoEffectSticker struct {
	VideoID     string      `json:"video_id"`
	StickerID   string      `json:"sticker_id"`
	StickerName pgtype.Text `json:"sticker_name"`
}

type TiktokVideoHashtag struct {
	VideoID string `json:"video_id"`
	Hashtag string `json:"hashtag"`
}

type TiktokVideoMention struct {
	VideoID       string `json:"video_id"`
	MentionedUser string `json:"mentioned_user"`
}

type TiktokVideosWithHashtag struct {
	ID                 string           `json:"id"`
	Text               pgtype.Text      `json:"text"`
	TextLanguage       pgtype.Text      `json:"text_language"`
	AuthorID           string           `json:"author_id"`
	MusicID            pgtype.Text      `json:"music_id"`
	UserID             int32            `json:"user_id"`
	VideoUrl           pgtype.Text      `json:"video_url"`
	CoverUrl           pgtype.Text      `json:"cover_url"`
	Width              pgtype.Int4      `json:"width"`
	Height             pgtype.Int4      `json:"height"`
	Duration           pgtype.Int4      `json:"duration"`
	Definition         pgtype.Text      `json:"definition"`
	CreateTime         pgtype.Timestamp `json:"create_time"`
	CommentCount       pgtype.Int4      `json:"comment_count"`
	DiggCount          pgtype.Int4      `json:"digg_count"`
	ShareCount         pgtype.Int4      `json:"share_count"`
	PlayCount          pgtype.Int8      `json:"play_count"`
	CollectCount       pgtype.Int4      `json:"collect_count"`
	CreateTimeIso      pgtype.Timestamp `json:"create_time_iso"`
	Input              pgtype.Text      `json:"input"`
	SearchHashtag      pgtype.Text      `json:"search_hashtag"`
	SearchHashtagViews pgtype.Int8      `json:"search_hashtag_views"`
	IsAd               pgtype.Bool      `json:"is_ad"`
	IsPinned           pgtype.Bool      `json:"is_pinned"`
	IsSponsored        pgtype.Bool      `json:"is_sponsored"`
	IsSlideshow        pgtype.Bool      `json:"is_slideshow"`
	ExpiresAt          pgtype.Timestamp `json:"expires_at"`
	CreatedAt          pgtype.Timestamp `json:"created_at"`
}

type User struct {
	ID                 int64              `json:"id"`
	GithubID           pgtype.Text        `json:"github_id"`
	TiktokID           pgtype.Text        `json:"tiktok_id"`
	Login              string             `json:"login"`
	Email              pgtype.Text        `json:"email"`
	Name               pgtype.Text        `json:"name"`
	AvatarUrl          pgtype.Text        `json:"avatar_url"`
	Token              pgtype.Text        `json:"token"`
	UserType           pgtype.Text        `json:"user_type"`
	AuthorUniqueIds    []string           `json:"author_unique_ids"`
	DailyAnalysisCount pgtype.Int4        `json:"daily_analysis_count"`
	LastAnalysisDate   pgtype.Date        `json:"last_analysis_date"`
	CreatedAt          pgtype.Timestamptz `json:"created_at"`
	UpdatedAt          pgtype.Timestamptz `json:"updated_at"`
}
