// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.25.0
// source: ai_analytics.sql

package db

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const insertAISortedComment = `-- name: InsertAISortedComment :exec
INSERT INTO ai_sorted_comments (
    video_url,user_id, text, intent_score, labels, urls, final_score, scenario_reinforcement
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8
)
`

type InsertAISortedCommentParams struct {
	VideoUrl              string        `json:"video_url"`
	UserID                int32         `json:"user_id"`
	Text                  pgtype.Text   `json:"text"`
	IntentScore           pgtype.Int4   `json:"intent_score"`
	Labels                []string      `json:"labels"`
	Urls                  []string      `json:"urls"`
	FinalScore            pgtype.Float8 `json:"final_score"`
	ScenarioReinforcement pgtype.Text   `json:"scenario_reinforcement"`
}

func (q *Queries) InsertAISortedComment(ctx context.Context, arg InsertAISortedCommentParams) error {
	_, err := q.db.Exec(ctx, insertAISortedComment,
		arg.VideoUrl,
		arg.UserID,
		arg.Text,
		arg.IntentScore,
		arg.Labels,
		arg.Urls,
		arg.FinalScore,
		arg.ScenarioReinforcement,
	)
	return err
}

const insertAISortedKoc = `-- name: InsertAISortedKoc :exec
INSERT INTO ai_sorted_kocs (
    video_url, user_id, intent_score, labels, urls, final_score, scenario_reinforcement
) VALUES (
    $1, $2, $3, $4, $5, $6, $7
)
`

type InsertAISortedKocParams struct {
	VideoUrl              string        `json:"video_url"`
	UserID                int32         `json:"user_id"`
	IntentScore           pgtype.Int4   `json:"intent_score"`
	Labels                []string      `json:"labels"`
	Urls                  []string      `json:"urls"`
	FinalScore            pgtype.Float8 `json:"final_score"`
	ScenarioReinforcement pgtype.Text   `json:"scenario_reinforcement"`
}

func (q *Queries) InsertAISortedKoc(ctx context.Context, arg InsertAISortedKocParams) error {
	_, err := q.db.Exec(ctx, insertAISortedKoc,
		arg.VideoUrl,
		arg.UserID,
		arg.IntentScore,
		arg.Labels,
		arg.Urls,
		arg.FinalScore,
		arg.ScenarioReinforcement,
	)
	return err
}

const listAISortedComments = `-- name: ListAISortedComments :many
SELECT
    id,
    video_url,
    user_id,
    text,
    intent_score,
    labels,
    urls,
    final_score,
    scenario_reinforcement,
    created_at
FROM
    ai_sorted_comments
WHERE
    user_id = $1
ORDER BY
    final_score DESC
LIMIT 
    $2 OFFSET $3
`

type ListAISortedCommentsParams struct {
	UserID int32 `json:"user_id"`
	Limit  int32 `json:"limit"`
	Offset int32 `json:"offset"`
}

type ListAISortedCommentsRow struct {
	ID                    int32              `json:"id"`
	VideoUrl              string             `json:"video_url"`
	UserID                int32              `json:"user_id"`
	Text                  pgtype.Text        `json:"text"`
	IntentScore           pgtype.Int4        `json:"intent_score"`
	Labels                []string           `json:"labels"`
	Urls                  []string           `json:"urls"`
	FinalScore            pgtype.Float8      `json:"final_score"`
	ScenarioReinforcement pgtype.Text        `json:"scenario_reinforcement"`
	CreatedAt             pgtype.Timestamptz `json:"created_at"`
}

func (q *Queries) ListAISortedComments(ctx context.Context, arg ListAISortedCommentsParams) ([]ListAISortedCommentsRow, error) {
	rows, err := q.db.Query(ctx, listAISortedComments, arg.UserID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ListAISortedCommentsRow
	for rows.Next() {
		var i ListAISortedCommentsRow
		if err := rows.Scan(
			&i.ID,
			&i.VideoUrl,
			&i.UserID,
			&i.Text,
			&i.IntentScore,
			&i.Labels,
			&i.Urls,
			&i.FinalScore,
			&i.ScenarioReinforcement,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listAISortedKocs = `-- name: ListAISortedKocs :many
SELECT
    id,
    video_url,
    user_id,
    intent_score,
    labels,
    urls,
    final_score,
    scenario_reinforcement,
    created_at
FROM
    ai_sorted_kocs
WHERE
    user_id = $1
ORDER BY
    final_score DESC
LIMIT 
    $2 OFFSET $3
`

type ListAISortedKocsParams struct {
	UserID int32 `json:"user_id"`
	Limit  int32 `json:"limit"`
	Offset int32 `json:"offset"`
}

type ListAISortedKocsRow struct {
	ID                    int32              `json:"id"`
	VideoUrl              string             `json:"video_url"`
	UserID                int32              `json:"user_id"`
	IntentScore           pgtype.Int4        `json:"intent_score"`
	Labels                []string           `json:"labels"`
	Urls                  []string           `json:"urls"`
	FinalScore            pgtype.Float8      `json:"final_score"`
	ScenarioReinforcement pgtype.Text        `json:"scenario_reinforcement"`
	CreatedAt             pgtype.Timestamptz `json:"created_at"`
}

func (q *Queries) ListAISortedKocs(ctx context.Context, arg ListAISortedKocsParams) ([]ListAISortedKocsRow, error) {
	rows, err := q.db.Query(ctx, listAISortedKocs, arg.UserID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ListAISortedKocsRow
	for rows.Next() {
		var i ListAISortedKocsRow
		if err := rows.Scan(
			&i.ID,
			&i.VideoUrl,
			&i.UserID,
			&i.IntentScore,
			&i.Labels,
			&i.Urls,
			&i.FinalScore,
			&i.ScenarioReinforcement,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
