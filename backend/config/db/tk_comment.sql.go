// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.25.0
// source: tk_comment.sql

package db

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const countTKCommentsByUserID = `-- name: CountTKCommentsByUserID :one
SELECT COUNT(*) FROM tiktok_comments WHERE user_id = $1
`

func (q *Queries) CountTKCommentsByUserID(ctx context.Context, userID int32) (int64, error) {
	row := q.db.QueryRow(ctx, countTKCommentsByUserID, userID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createTKComment = `-- name: CreateTKComment :exec
INSERT INTO tiktok_comments (
			user_id, video_url, comment_id, comment_text,
			author_uid, author_unique_id, avatar_url, digg_count,
			liked_by_author, pinned_by_author, reply_comment_total,
			comment_time, expires_at
)
VALUES (
			$1, $2, $3, $4,
			$5, $6, $7, $8,
			$9, $10, $11,
			$12, $13
)
ON CONFLICT (comment_id) DO NOTHING
`

type CreateTKCommentParams struct {
	UserID            int32            `json:"user_id"`
	VideoUrl          string           `json:"video_url"`
	CommentID         string           `json:"comment_id"`
	CommentText       string           `json:"comment_text"`
	AuthorUid         pgtype.Text      `json:"author_uid"`
	AuthorUniqueID    pgtype.Text      `json:"author_unique_id"`
	AvatarUrl         pgtype.Text      `json:"avatar_url"`
	DiggCount         pgtype.Int4      `json:"digg_count"`
	LikedByAuthor     pgtype.Bool      `json:"liked_by_author"`
	PinnedByAuthor    pgtype.Bool      `json:"pinned_by_author"`
	ReplyCommentTotal pgtype.Int4      `json:"reply_comment_total"`
	CommentTime       pgtype.Timestamp `json:"comment_time"`
	ExpiresAt         pgtype.Timestamp `json:"expires_at"`
}

func (q *Queries) CreateTKComment(ctx context.Context, arg CreateTKCommentParams) error {
	_, err := q.db.Exec(ctx, createTKComment,
		arg.UserID,
		arg.VideoUrl,
		arg.CommentID,
		arg.CommentText,
		arg.AuthorUid,
		arg.AuthorUniqueID,
		arg.AvatarUrl,
		arg.DiggCount,
		arg.LikedByAuthor,
		arg.PinnedByAuthor,
		arg.ReplyCommentTotal,
		arg.CommentTime,
		arg.ExpiresAt,
	)
	return err
}

const listTKCommentsByIDs = `-- name: ListTKCommentsByIDs :many
SELECT id, user_id, video_url, comment_id, comment_text, author_uid, author_unique_id, avatar_url, digg_count, liked_by_author, pinned_by_author, reply_comment_total, created_at, comment_time, expires_at
FROM tiktok_comments
WHERE comment_id = ANY($1::text[])
ORDER BY comment_time DESC
`

func (q *Queries) ListTKCommentsByIDs(ctx context.Context, dollar_1 []string) ([]TiktokComment, error) {
	rows, err := q.db.Query(ctx, listTKCommentsByIDs, dollar_1)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []TiktokComment
	for rows.Next() {
		var i TiktokComment
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.VideoUrl,
			&i.CommentID,
			&i.CommentText,
			&i.AuthorUid,
			&i.AuthorUniqueID,
			&i.AvatarUrl,
			&i.DiggCount,
			&i.LikedByAuthor,
			&i.PinnedByAuthor,
			&i.ReplyCommentTotal,
			&i.CreatedAt,
			&i.CommentTime,
			&i.ExpiresAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listTKCommentsByUserID = `-- name: ListTKCommentsByUserID :many
SELECT id, user_id, video_url, comment_id, comment_text, author_uid, author_unique_id, avatar_url, digg_count, liked_by_author, pinned_by_author, reply_comment_total, created_at, comment_time, expires_at
FROM tiktok_comments
WHERE user_id = $1
ORDER BY comment_time DESC
LIMIT $2 OFFSET $3
`

type ListTKCommentsByUserIDParams struct {
	UserID int32 `json:"user_id"`
	Limit  int32 `json:"limit"`
	Offset int32 `json:"offset"`
}

func (q *Queries) ListTKCommentsByUserID(ctx context.Context, arg ListTKCommentsByUserIDParams) ([]TiktokComment, error) {
	rows, err := q.db.Query(ctx, listTKCommentsByUserID, arg.UserID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []TiktokComment
	for rows.Next() {
		var i TiktokComment
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.VideoUrl,
			&i.CommentID,
			&i.CommentText,
			&i.AuthorUid,
			&i.AuthorUniqueID,
			&i.AvatarUrl,
			&i.DiggCount,
			&i.LikedByAuthor,
			&i.PinnedByAuthor,
			&i.ReplyCommentTotal,
			&i.CreatedAt,
			&i.CommentTime,
			&i.ExpiresAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
