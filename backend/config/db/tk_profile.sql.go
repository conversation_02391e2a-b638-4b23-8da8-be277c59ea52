// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.25.0
// source: tk_profile.sql

package db

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const createTKVideo = `-- name: CreateTKVideo :exec
INSERT INTO tiktok_videos (
    id, author_id, music_id, user_id, text, text_language,
    create_time, is_ad, video_url,
    height, width, duration, cover_url,
    definition, digg_count, share_count, play_count, collect_count, comment_count,
    is_slideshow, is_pinned, is_sponsored,
    expires_at
) VALUES (
    $1, $2, $3, $4, $5, $6,
    $7, $8, $9,
    $10, $11, $12, $13,
    $14, $15, $16, $17, $18, $19,
    $20, $21, $22,
    $23
)
ON CONFLICT (id) DO NOTHING
`

type CreateTKVideoParams struct {
	ID           string           `json:"id"`
	AuthorID     string           `json:"author_id"`
	MusicID      pgtype.Text      `json:"music_id"`
	UserID       int32            `json:"user_id"`
	Text         pgtype.Text      `json:"text"`
	TextLanguage pgtype.Text      `json:"text_language"`
	CreateTime   pgtype.Timestamp `json:"create_time"`
	IsAd         pgtype.Bool      `json:"is_ad"`
	VideoUrl     pgtype.Text      `json:"video_url"`
	Height       pgtype.Int4      `json:"height"`
	Width        pgtype.Int4      `json:"width"`
	Duration     pgtype.Int4      `json:"duration"`
	CoverUrl     pgtype.Text      `json:"cover_url"`
	Definition   pgtype.Text      `json:"definition"`
	DiggCount    pgtype.Int4      `json:"digg_count"`
	ShareCount   pgtype.Int4      `json:"share_count"`
	PlayCount    pgtype.Int8      `json:"play_count"`
	CollectCount pgtype.Int4      `json:"collect_count"`
	CommentCount pgtype.Int4      `json:"comment_count"`
	IsSlideshow  pgtype.Bool      `json:"is_slideshow"`
	IsPinned     pgtype.Bool      `json:"is_pinned"`
	IsSponsored  pgtype.Bool      `json:"is_sponsored"`
	ExpiresAt    pgtype.Timestamp `json:"expires_at"`
}

func (q *Queries) CreateTKVideo(ctx context.Context, arg CreateTKVideoParams) error {
	_, err := q.db.Exec(ctx, createTKVideo,
		arg.ID,
		arg.AuthorID,
		arg.MusicID,
		arg.UserID,
		arg.Text,
		arg.TextLanguage,
		arg.CreateTime,
		arg.IsAd,
		arg.VideoUrl,
		arg.Height,
		arg.Width,
		arg.Duration,
		arg.CoverUrl,
		arg.Definition,
		arg.DiggCount,
		arg.ShareCount,
		arg.PlayCount,
		arg.CollectCount,
		arg.CommentCount,
		arg.IsSlideshow,
		arg.IsPinned,
		arg.IsSponsored,
		arg.ExpiresAt,
	)
	return err
}

const createTKVideoAuthor = `-- name: CreateTKVideoAuthor :exec
INSERT INTO tiktok_authors (
    id, username, nickname, profile_url, signature, bio_link,
    avatar_url, original_avatar_url, verified, private_account, region,
    following, friends, fans, heart, video_count, digg_count
) VALUES (
    $1, $2, $3, $4, $5, $6,
    $7, $8, $9, $10, $11,
    $12, $13, $14, $15, $16, $17
)
ON CONFLICT (id) DO NOTHING
`

type CreateTKVideoAuthorParams struct {
	ID                string      `json:"id"`
	Username          pgtype.Text `json:"username"`
	Nickname          pgtype.Text `json:"nickname"`
	ProfileUrl        pgtype.Text `json:"profile_url"`
	Signature         pgtype.Text `json:"signature"`
	BioLink           pgtype.Text `json:"bio_link"`
	AvatarUrl         pgtype.Text `json:"avatar_url"`
	OriginalAvatarUrl pgtype.Text `json:"original_avatar_url"`
	Verified          pgtype.Bool `json:"verified"`
	PrivateAccount    pgtype.Bool `json:"private_account"`
	Region            pgtype.Text `json:"region"`
	Following         pgtype.Int4 `json:"following"`
	Friends           pgtype.Int4 `json:"friends"`
	Fans              pgtype.Int4 `json:"fans"`
	Heart             pgtype.Int8 `json:"heart"`
	VideoCount        pgtype.Int4 `json:"video_count"`
	DiggCount         pgtype.Int4 `json:"digg_count"`
}

func (q *Queries) CreateTKVideoAuthor(ctx context.Context, arg CreateTKVideoAuthorParams) error {
	_, err := q.db.Exec(ctx, createTKVideoAuthor,
		arg.ID,
		arg.Username,
		arg.Nickname,
		arg.ProfileUrl,
		arg.Signature,
		arg.BioLink,
		arg.AvatarUrl,
		arg.OriginalAvatarUrl,
		arg.Verified,
		arg.PrivateAccount,
		arg.Region,
		arg.Following,
		arg.Friends,
		arg.Fans,
		arg.Heart,
		arg.VideoCount,
		arg.DiggCount,
	)
	return err
}

const createTKVideoHashtagsAndMentions = `-- name: CreateTKVideoHashtagsAndMentions :exec
INSERT INTO tiktok_video_hashtags (video_id, hashtag) VALUES ($1, $2)
ON CONFLICT DO NOTHING
`

type CreateTKVideoHashtagsAndMentionsParams struct {
	VideoID string `json:"video_id"`
	Hashtag string `json:"hashtag"`
}

func (q *Queries) CreateTKVideoHashtagsAndMentions(ctx context.Context, arg CreateTKVideoHashtagsAndMentionsParams) error {
	_, err := q.db.Exec(ctx, createTKVideoHashtagsAndMentions, arg.VideoID, arg.Hashtag)
	return err
}

const createTKVideoMentions = `-- name: CreateTKVideoMentions :exec
INSERT INTO tiktok_video_mentions (video_id, mentioned_user) VALUES ($1, $2)
ON CONFLICT DO NOTHING
`

type CreateTKVideoMentionsParams struct {
	VideoID       string `json:"video_id"`
	MentionedUser string `json:"mentioned_user"`
}

func (q *Queries) CreateTKVideoMentions(ctx context.Context, arg CreateTKVideoMentionsParams) error {
	_, err := q.db.Exec(ctx, createTKVideoMentions, arg.VideoID, arg.MentionedUser)
	return err
}

const createTKVideoMusic = `-- name: CreateTKVideoMusic :exec
INSERT INTO tiktok_music (
    id, name, author, is_original, play_url,
    cover_url, original_cover_url
) VALUES (
    $1, $2, $3, $4, $5,
    $6, $7
)
ON CONFLICT (id) DO NOTHING
`

type CreateTKVideoMusicParams struct {
	ID               string      `json:"id"`
	Name             pgtype.Text `json:"name"`
	Author           pgtype.Text `json:"author"`
	IsOriginal       pgtype.Bool `json:"is_original"`
	PlayUrl          pgtype.Text `json:"play_url"`
	CoverUrl         pgtype.Text `json:"cover_url"`
	OriginalCoverUrl pgtype.Text `json:"original_cover_url"`
}

func (q *Queries) CreateTKVideoMusic(ctx context.Context, arg CreateTKVideoMusicParams) error {
	_, err := q.db.Exec(ctx, createTKVideoMusic,
		arg.ID,
		arg.Name,
		arg.Author,
		arg.IsOriginal,
		arg.PlayUrl,
		arg.CoverUrl,
		arg.OriginalCoverUrl,
	)
	return err
}

const listTKAuthorsByAuthorIDs = `-- name: ListTKAuthorsByAuthorIDs :many
SELECT id, text, text_language, author_id, music_id, user_id, video_url, cover_url, width, height, duration, definition, create_time, comment_count, digg_count, share_count, play_count, collect_count, is_ad, is_pinned, is_sponsored, is_slideshow, expires_at, created_at
FROM tiktok_videos
WHERE author_id = ANY($1::text[])
ORDER BY created_at DESC
`

func (q *Queries) ListTKAuthorsByAuthorIDs(ctx context.Context, dollar_1 []string) ([]TiktokVideo, error) {
	rows, err := q.db.Query(ctx, listTKAuthorsByAuthorIDs, dollar_1)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []TiktokVideo
	for rows.Next() {
		var i TiktokVideo
		if err := rows.Scan(
			&i.ID,
			&i.Text,
			&i.TextLanguage,
			&i.AuthorID,
			&i.MusicID,
			&i.UserID,
			&i.VideoUrl,
			&i.CoverUrl,
			&i.Width,
			&i.Height,
			&i.Duration,
			&i.Definition,
			&i.CreateTime,
			&i.CommentCount,
			&i.DiggCount,
			&i.ShareCount,
			&i.PlayCount,
			&i.CollectCount,
			&i.IsAd,
			&i.IsPinned,
			&i.IsSponsored,
			&i.IsSlideshow,
			&i.ExpiresAt,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listTKAuthorsByIDs = `-- name: ListTKAuthorsByIDs :many
SELECT id, username, nickname, profile_url, avatar_url, original_avatar_url, signature, bio_link, verified, region, private_account, fans, heart, following, friends, video_count, digg_count, created_at
FROM tiktok_authors
WHERE username = ANY($1::text[])
ORDER BY id
`

func (q *Queries) ListTKAuthorsByIDs(ctx context.Context, dollar_1 []string) ([]TiktokAuthor, error) {
	rows, err := q.db.Query(ctx, listTKAuthorsByIDs, dollar_1)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []TiktokAuthor
	for rows.Next() {
		var i TiktokAuthor
		if err := rows.Scan(
			&i.ID,
			&i.Username,
			&i.Nickname,
			&i.ProfileUrl,
			&i.AvatarUrl,
			&i.OriginalAvatarUrl,
			&i.Signature,
			&i.BioLink,
			&i.Verified,
			&i.Region,
			&i.PrivateAccount,
			&i.Fans,
			&i.Heart,
			&i.Following,
			&i.Friends,
			&i.VideoCount,
			&i.DiggCount,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listTKVideosWithDetails = `-- name: ListTKVideosWithDetails :many
SELECT 
    v.id AS video_id,
    v.text AS video_text,
    v.text_language,
    v.video_url,
    v.cover_url,
    v.create_time,
    v.duration,
    v.definition,
    v.comment_count,
    v.digg_count,
    v.share_count,
    v.play_count,
    v.collect_count,
    v.is_ad,
    v.is_pinned,
    v.is_sponsored,
    v.is_slideshow,
    v.user_id,
    v.expires_at,
    v.created_at,

    -- author fields
    a.id AS author_id,
    a.username AS author_username,
    a.nickname AS author_nickname,
    a.avatar_url AS author_avatar_url,
    a.signature AS author_signature,

    -- music fields
    m.id AS music_id,
    m.name AS music_name,
    m.author AS music_author,
    m.play_url AS music_play_url

FROM tiktok_videos v
LEFT JOIN tiktok_authors a ON v.author_id = a.id
LEFT JOIN tiktok_music m ON v.music_id = m.id
WHERE v.user_id = $1
ORDER BY v.create_time DESC
LIMIT $2 OFFSET $3
`

type ListTKVideosWithDetailsParams struct {
	UserID int32 `json:"user_id"`
	Limit  int32 `json:"limit"`
	Offset int32 `json:"offset"`
}

type ListTKVideosWithDetailsRow struct {
	VideoID         string           `json:"video_id"`
	VideoText       pgtype.Text      `json:"video_text"`
	TextLanguage    pgtype.Text      `json:"text_language"`
	VideoUrl        pgtype.Text      `json:"video_url"`
	CoverUrl        pgtype.Text      `json:"cover_url"`
	CreateTime      pgtype.Timestamp `json:"create_time"`
	Duration        pgtype.Int4      `json:"duration"`
	Definition      pgtype.Text      `json:"definition"`
	CommentCount    pgtype.Int4      `json:"comment_count"`
	DiggCount       pgtype.Int4      `json:"digg_count"`
	ShareCount      pgtype.Int4      `json:"share_count"`
	PlayCount       pgtype.Int8      `json:"play_count"`
	CollectCount    pgtype.Int4      `json:"collect_count"`
	IsAd            pgtype.Bool      `json:"is_ad"`
	IsPinned        pgtype.Bool      `json:"is_pinned"`
	IsSponsored     pgtype.Bool      `json:"is_sponsored"`
	IsSlideshow     pgtype.Bool      `json:"is_slideshow"`
	UserID          int32            `json:"user_id"`
	ExpiresAt       pgtype.Timestamp `json:"expires_at"`
	CreatedAt       pgtype.Timestamp `json:"created_at"`
	AuthorID        pgtype.Text      `json:"author_id"`
	AuthorUsername  pgtype.Text      `json:"author_username"`
	AuthorNickname  pgtype.Text      `json:"author_nickname"`
	AuthorAvatarUrl pgtype.Text      `json:"author_avatar_url"`
	AuthorSignature pgtype.Text      `json:"author_signature"`
	MusicID         pgtype.Text      `json:"music_id"`
	MusicName       pgtype.Text      `json:"music_name"`
	MusicAuthor     pgtype.Text      `json:"music_author"`
	MusicPlayUrl    pgtype.Text      `json:"music_play_url"`
}

func (q *Queries) ListTKVideosWithDetails(ctx context.Context, arg ListTKVideosWithDetailsParams) ([]ListTKVideosWithDetailsRow, error) {
	rows, err := q.db.Query(ctx, listTKVideosWithDetails, arg.UserID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ListTKVideosWithDetailsRow
	for rows.Next() {
		var i ListTKVideosWithDetailsRow
		if err := rows.Scan(
			&i.VideoID,
			&i.VideoText,
			&i.TextLanguage,
			&i.VideoUrl,
			&i.CoverUrl,
			&i.CreateTime,
			&i.Duration,
			&i.Definition,
			&i.CommentCount,
			&i.DiggCount,
			&i.ShareCount,
			&i.PlayCount,
			&i.CollectCount,
			&i.IsAd,
			&i.IsPinned,
			&i.IsSponsored,
			&i.IsSlideshow,
			&i.UserID,
			&i.ExpiresAt,
			&i.CreatedAt,
			&i.AuthorID,
			&i.AuthorUsername,
			&i.AuthorNickname,
			&i.AuthorAvatarUrl,
			&i.AuthorSignature,
			&i.MusicID,
			&i.MusicName,
			&i.MusicAuthor,
			&i.MusicPlayUrl,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
