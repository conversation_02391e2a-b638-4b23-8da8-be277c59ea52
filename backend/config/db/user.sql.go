// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.25.0
// source: user.sql

package db

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const createGithubUser = `-- name: CreateGithubUser :one
INSERT INTO users (
  github_id, login, email, name, avatar_url, token, user_type
) VALUES (
  $1, $2, $3, $4, $5, $6, $7
)
RETURNING id, github_id, tiktok_id, login, email, name, avatar_url, token, user_type, author_unique_ids, daily_analysis_count, last_analysis_date, created_at, updated_at
`

type CreateGithubUserParams struct {
	GithubID  pgtype.Text `json:"github_id"`
	Login     string      `json:"login"`
	Email     pgtype.Text `json:"email"`
	Name      pgtype.Text `json:"name"`
	AvatarUrl pgtype.Text `json:"avatar_url"`
	Token     pgtype.Text `json:"token"`
	UserType  pgtype.Text `json:"user_type"`
}

func (q *Queries) CreateGithubUser(ctx context.Context, arg CreateGithubUserParams) (User, error) {
	row := q.db.QueryRow(ctx, createGithubUser,
		arg.GithubID,
		arg.Login,
		arg.Email,
		arg.Name,
		arg.AvatarUrl,
		arg.Token,
		arg.UserType,
	)
	var i User
	err := row.Scan(
		&i.ID,
		&i.GithubID,
		&i.TiktokID,
		&i.Login,
		&i.Email,
		&i.Name,
		&i.AvatarUrl,
		&i.Token,
		&i.UserType,
		&i.AuthorUniqueIds,
		&i.DailyAnalysisCount,
		&i.LastAnalysisDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createTikTokUser = `-- name: CreateTikTokUser :one
INSERT INTO users (
  tiktok_id, login, email, name, avatar_url, token, user_type
) VALUES (
  $1, $2, $3, $4, $5, $6, $7
)
RETURNING id, github_id, tiktok_id, login, email, name, avatar_url, token, user_type, author_unique_ids, daily_analysis_count, last_analysis_date, created_at, updated_at
`

type CreateTikTokUserParams struct {
	TiktokID  pgtype.Text `json:"tiktok_id"`
	Login     string      `json:"login"`
	Email     pgtype.Text `json:"email"`
	Name      pgtype.Text `json:"name"`
	AvatarUrl pgtype.Text `json:"avatar_url"`
	Token     pgtype.Text `json:"token"`
	UserType  pgtype.Text `json:"user_type"`
}

func (q *Queries) CreateTikTokUser(ctx context.Context, arg CreateTikTokUserParams) (User, error) {
	row := q.db.QueryRow(ctx, createTikTokUser,
		arg.TiktokID,
		arg.Login,
		arg.Email,
		arg.Name,
		arg.AvatarUrl,
		arg.Token,
		arg.UserType,
	)
	var i User
	err := row.Scan(
		&i.ID,
		&i.GithubID,
		&i.TiktokID,
		&i.Login,
		&i.Email,
		&i.Name,
		&i.AvatarUrl,
		&i.Token,
		&i.UserType,
		&i.AuthorUniqueIds,
		&i.DailyAnalysisCount,
		&i.LastAnalysisDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getUser = `-- name: GetUser :one
SELECT id, github_id, tiktok_id, login, email, name, avatar_url, token, user_type, author_unique_ids, daily_analysis_count, last_analysis_date, created_at, updated_at FROM users
WHERE name = $1 LIMIT 1
`

func (q *Queries) GetUser(ctx context.Context, name pgtype.Text) (User, error) {
	row := q.db.QueryRow(ctx, getUser, name)
	var i User
	err := row.Scan(
		&i.ID,
		&i.GithubID,
		&i.TiktokID,
		&i.Login,
		&i.Email,
		&i.Name,
		&i.AvatarUrl,
		&i.Token,
		&i.UserType,
		&i.AuthorUniqueIds,
		&i.DailyAnalysisCount,
		&i.LastAnalysisDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getUserAnalysisCount = `-- name: GetUserAnalysisCount :one
SELECT daily_analysis_count, last_analysis_date
FROM users
WHERE id = $1
`

type GetUserAnalysisCountRow struct {
	DailyAnalysisCount pgtype.Int4 `json:"daily_analysis_count"`
	LastAnalysisDate   pgtype.Date `json:"last_analysis_date"`
}

func (q *Queries) GetUserAnalysisCount(ctx context.Context, id int64) (GetUserAnalysisCountRow, error) {
	row := q.db.QueryRow(ctx, getUserAnalysisCount, id)
	var i GetUserAnalysisCountRow
	err := row.Scan(&i.DailyAnalysisCount, &i.LastAnalysisDate)
	return i, err
}

const getUserByGithubID = `-- name: GetUserByGithubID :one
SELECT id, github_id, tiktok_id, login, email, name, avatar_url, token, user_type, author_unique_ids, daily_analysis_count, last_analysis_date, created_at, updated_at FROM users
WHERE github_id = $1
`

func (q *Queries) GetUserByGithubID(ctx context.Context, githubID pgtype.Text) (User, error) {
	row := q.db.QueryRow(ctx, getUserByGithubID, githubID)
	var i User
	err := row.Scan(
		&i.ID,
		&i.GithubID,
		&i.TiktokID,
		&i.Login,
		&i.Email,
		&i.Name,
		&i.AvatarUrl,
		&i.Token,
		&i.UserType,
		&i.AuthorUniqueIds,
		&i.DailyAnalysisCount,
		&i.LastAnalysisDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getUserByTikTokID = `-- name: GetUserByTikTokID :one
SELECT id, github_id, tiktok_id, login, email, name, avatar_url, token, user_type, author_unique_ids, daily_analysis_count, last_analysis_date, created_at, updated_at FROM users
WHERE tiktok_id = $1
`

func (q *Queries) GetUserByTikTokID(ctx context.Context, tiktokID pgtype.Text) (User, error) {
	row := q.db.QueryRow(ctx, getUserByTikTokID, tiktokID)
	var i User
	err := row.Scan(
		&i.ID,
		&i.GithubID,
		&i.TiktokID,
		&i.Login,
		&i.Email,
		&i.Name,
		&i.AvatarUrl,
		&i.Token,
		&i.UserType,
		&i.AuthorUniqueIds,
		&i.DailyAnalysisCount,
		&i.LastAnalysisDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getUserFromCode = `-- name: GetUserFromCode :one
SELECT id, github_id, tiktok_id, login, email, name, avatar_url, token, user_type, author_unique_ids, daily_analysis_count, last_analysis_date, created_at, updated_at FROM users
WHERE token = $1 LIMIT 1
`

func (q *Queries) GetUserFromCode(ctx context.Context, token pgtype.Text) (User, error) {
	row := q.db.QueryRow(ctx, getUserFromCode, token)
	var i User
	err := row.Scan(
		&i.ID,
		&i.GithubID,
		&i.TiktokID,
		&i.Login,
		&i.Email,
		&i.Name,
		&i.AvatarUrl,
		&i.Token,
		&i.UserType,
		&i.AuthorUniqueIds,
		&i.DailyAnalysisCount,
		&i.LastAnalysisDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getUserWithID = `-- name: GetUserWithID :one
SELECT id, github_id, tiktok_id, login, email, name, avatar_url, token, user_type, author_unique_ids, daily_analysis_count, last_analysis_date, created_at, updated_at FROM users
WHERE id = $1
`

func (q *Queries) GetUserWithID(ctx context.Context, id int64) (User, error) {
	row := q.db.QueryRow(ctx, getUserWithID, id)
	var i User
	err := row.Scan(
		&i.ID,
		&i.GithubID,
		&i.TiktokID,
		&i.Login,
		&i.Email,
		&i.Name,
		&i.AvatarUrl,
		&i.Token,
		&i.UserType,
		&i.AuthorUniqueIds,
		&i.DailyAnalysisCount,
		&i.LastAnalysisDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getUsersByType = `-- name: GetUsersByType :many
SELECT id, github_id, tiktok_id, login, email, name, avatar_url, token, user_type, author_unique_ids, daily_analysis_count, last_analysis_date, created_at, updated_at FROM users
WHERE user_type = $1
`

func (q *Queries) GetUsersByType(ctx context.Context, userType pgtype.Text) ([]User, error) {
	rows, err := q.db.Query(ctx, getUsersByType, userType)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []User
	for rows.Next() {
		var i User
		if err := rows.Scan(
			&i.ID,
			&i.GithubID,
			&i.TiktokID,
			&i.Login,
			&i.Email,
			&i.Name,
			&i.AvatarUrl,
			&i.Token,
			&i.UserType,
			&i.AuthorUniqueIds,
			&i.DailyAnalysisCount,
			&i.LastAnalysisDate,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const incrementAnalysisCount = `-- name: IncrementAnalysisCount :one
UPDATE users
SET daily_analysis_count = CASE
    WHEN last_analysis_date = CURRENT_DATE THEN daily_analysis_count + 1
    ELSE 1
END,
last_analysis_date = CURRENT_DATE,
updated_at = now()
WHERE id = $1
RETURNING id, github_id, tiktok_id, login, email, name, avatar_url, token, user_type, author_unique_ids, daily_analysis_count, last_analysis_date, created_at, updated_at
`

func (q *Queries) IncrementAnalysisCount(ctx context.Context, id int64) (User, error) {
	row := q.db.QueryRow(ctx, incrementAnalysisCount, id)
	var i User
	err := row.Scan(
		&i.ID,
		&i.GithubID,
		&i.TiktokID,
		&i.Login,
		&i.Email,
		&i.Name,
		&i.AvatarUrl,
		&i.Token,
		&i.UserType,
		&i.AuthorUniqueIds,
		&i.DailyAnalysisCount,
		&i.LastAnalysisDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateUserAuthorUniqueIDs = `-- name: UpdateUserAuthorUniqueIDs :one
UPDATE users
SET author_unique_ids = $2
WHERE id = $1
RETURNING id, github_id, tiktok_id, login, email, name, avatar_url, token, user_type, author_unique_ids, daily_analysis_count, last_analysis_date, created_at, updated_at
`

type UpdateUserAuthorUniqueIDsParams struct {
	ID              int64    `json:"id"`
	AuthorUniqueIds []string `json:"author_unique_ids"`
}

func (q *Queries) UpdateUserAuthorUniqueIDs(ctx context.Context, arg UpdateUserAuthorUniqueIDsParams) (User, error) {
	row := q.db.QueryRow(ctx, updateUserAuthorUniqueIDs, arg.ID, arg.AuthorUniqueIds)
	var i User
	err := row.Scan(
		&i.ID,
		&i.GithubID,
		&i.TiktokID,
		&i.Login,
		&i.Email,
		&i.Name,
		&i.AvatarUrl,
		&i.Token,
		&i.UserType,
		&i.AuthorUniqueIds,
		&i.DailyAnalysisCount,
		&i.LastAnalysisDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateUserByGithubID = `-- name: UpdateUserByGithubID :one
UPDATE users
SET login = $2,
    email = $3,
    name = $4,
    avatar_url = $5,
    token = $6,
    user_type = $7,
    updated_at = now()
WHERE github_id = $1
RETURNING id, github_id, tiktok_id, login, email, name, avatar_url, token, user_type, author_unique_ids, daily_analysis_count, last_analysis_date, created_at, updated_at
`

type UpdateUserByGithubIDParams struct {
	GithubID  pgtype.Text `json:"github_id"`
	Login     string      `json:"login"`
	Email     pgtype.Text `json:"email"`
	Name      pgtype.Text `json:"name"`
	AvatarUrl pgtype.Text `json:"avatar_url"`
	Token     pgtype.Text `json:"token"`
	UserType  pgtype.Text `json:"user_type"`
}

func (q *Queries) UpdateUserByGithubID(ctx context.Context, arg UpdateUserByGithubIDParams) (User, error) {
	row := q.db.QueryRow(ctx, updateUserByGithubID,
		arg.GithubID,
		arg.Login,
		arg.Email,
		arg.Name,
		arg.AvatarUrl,
		arg.Token,
		arg.UserType,
	)
	var i User
	err := row.Scan(
		&i.ID,
		&i.GithubID,
		&i.TiktokID,
		&i.Login,
		&i.Email,
		&i.Name,
		&i.AvatarUrl,
		&i.Token,
		&i.UserType,
		&i.AuthorUniqueIds,
		&i.DailyAnalysisCount,
		&i.LastAnalysisDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateUserByTikTokID = `-- name: UpdateUserByTikTokID :one
UPDATE users
SET login = $2,
    email = $3,
    name = $4,
    avatar_url = $5,
    token = $6,
    user_type = $7,
    updated_at = now()
WHERE tiktok_id = $1
RETURNING id, github_id, tiktok_id, login, email, name, avatar_url, token, user_type, author_unique_ids, daily_analysis_count, last_analysis_date, created_at, updated_at
`

type UpdateUserByTikTokIDParams struct {
	TiktokID  pgtype.Text `json:"tiktok_id"`
	Login     string      `json:"login"`
	Email     pgtype.Text `json:"email"`
	Name      pgtype.Text `json:"name"`
	AvatarUrl pgtype.Text `json:"avatar_url"`
	Token     pgtype.Text `json:"token"`
	UserType  pgtype.Text `json:"user_type"`
}

func (q *Queries) UpdateUserByTikTokID(ctx context.Context, arg UpdateUserByTikTokIDParams) (User, error) {
	row := q.db.QueryRow(ctx, updateUserByTikTokID,
		arg.TiktokID,
		arg.Login,
		arg.Email,
		arg.Name,
		arg.AvatarUrl,
		arg.Token,
		arg.UserType,
	)
	var i User
	err := row.Scan(
		&i.ID,
		&i.GithubID,
		&i.TiktokID,
		&i.Login,
		&i.Email,
		&i.Name,
		&i.AvatarUrl,
		&i.Token,
		&i.UserType,
		&i.AuthorUniqueIds,
		&i.DailyAnalysisCount,
		&i.LastAnalysisDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateUserType = `-- name: UpdateUserType :one
UPDATE users
SET user_type = $2,
    updated_at = now()
WHERE id = $1
RETURNING id, github_id, tiktok_id, login, email, name, avatar_url, token, user_type, author_unique_ids, daily_analysis_count, last_analysis_date, created_at, updated_at
`

type UpdateUserTypeParams struct {
	ID       int64       `json:"id"`
	UserType pgtype.Text `json:"user_type"`
}

func (q *Queries) UpdateUserType(ctx context.Context, arg UpdateUserTypeParams) (User, error) {
	row := q.db.QueryRow(ctx, updateUserType, arg.ID, arg.UserType)
	var i User
	err := row.Scan(
		&i.ID,
		&i.GithubID,
		&i.TiktokID,
		&i.Login,
		&i.Email,
		&i.Name,
		&i.AvatarUrl,
		&i.Token,
		&i.UserType,
		&i.AuthorUniqueIds,
		&i.DailyAnalysisCount,
		&i.LastAnalysisDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}
