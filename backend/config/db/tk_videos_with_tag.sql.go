// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.25.0
// source: tk_videos_with_tag.sql

package db

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const createTKVideoEffectSticker = `-- name: CreateTKVideoEffectSticker :exec
INSERT INTO tiktok_video_effect_stickers (
    video_id, sticker_id, sticker_name
) VALUES (
    $1, $2, $3
)
ON CONFLICT (video_id, sticker_id) DO NOTHING
`

type CreateTKVideoEffectStickerParams struct {
	VideoID     string      `json:"video_id"`
	StickerID   string      `json:"sticker_id"`
	StickerName pgtype.Text `json:"sticker_name"`
}

func (q *Queries) CreateTKVideoEffectSticker(ctx context.Context, arg CreateTKVideoEffectStickerParams) error {
	_, err := q.db.Exec(ctx, createTKVideoEffectSticker, arg.VideoID, arg.StickerID, arg.StickerName)
	return err
}

const createTKVideoWithHashTags = `-- name: CreateTKVideoWithHashTags :exec
INSERT INTO tiktok_videos_with_hashtags (
    id, text, text_language, author_id, music_id, user_id,
    video_url, cover_url, width, height, duration, definition,
    create_time, create_time_iso, input, search_hashtag, search_hashtag_views,
    comment_count, digg_count, share_count, play_count, collect_count,
    is_ad, is_pinned, is_sponsored, is_slideshow, expires_at
) VALUES (
    $1, $2, $3, $4, $5, $6,
    $7, $8, $9, $10, $11, $12,
    $13, $14, $15, $16, $17,
    $18, $19, $20, $21, $22,
    $23, $24, $25, $26, $27
)
ON CONFLICT (id) DO NOTHING
`

type CreateTKVideoWithHashTagsParams struct {
	ID                 string           `json:"id"`
	Text               pgtype.Text      `json:"text"`
	TextLanguage       pgtype.Text      `json:"text_language"`
	AuthorID           string           `json:"author_id"`
	MusicID            pgtype.Text      `json:"music_id"`
	UserID             int32            `json:"user_id"`
	VideoUrl           pgtype.Text      `json:"video_url"`
	CoverUrl           pgtype.Text      `json:"cover_url"`
	Width              pgtype.Int4      `json:"width"`
	Height             pgtype.Int4      `json:"height"`
	Duration           pgtype.Int4      `json:"duration"`
	Definition         pgtype.Text      `json:"definition"`
	CreateTime         pgtype.Timestamp `json:"create_time"`
	CreateTimeIso      pgtype.Timestamp `json:"create_time_iso"`
	Input              pgtype.Text      `json:"input"`
	SearchHashtag      pgtype.Text      `json:"search_hashtag"`
	SearchHashtagViews pgtype.Int8      `json:"search_hashtag_views"`
	CommentCount       pgtype.Int4      `json:"comment_count"`
	DiggCount          pgtype.Int4      `json:"digg_count"`
	ShareCount         pgtype.Int4      `json:"share_count"`
	PlayCount          pgtype.Int8      `json:"play_count"`
	CollectCount       pgtype.Int4      `json:"collect_count"`
	IsAd               pgtype.Bool      `json:"is_ad"`
	IsPinned           pgtype.Bool      `json:"is_pinned"`
	IsSponsored        pgtype.Bool      `json:"is_sponsored"`
	IsSlideshow        pgtype.Bool      `json:"is_slideshow"`
	ExpiresAt          pgtype.Timestamp `json:"expires_at"`
}

func (q *Queries) CreateTKVideoWithHashTags(ctx context.Context, arg CreateTKVideoWithHashTagsParams) error {
	_, err := q.db.Exec(ctx, createTKVideoWithHashTags,
		arg.ID,
		arg.Text,
		arg.TextLanguage,
		arg.AuthorID,
		arg.MusicID,
		arg.UserID,
		arg.VideoUrl,
		arg.CoverUrl,
		arg.Width,
		arg.Height,
		arg.Duration,
		arg.Definition,
		arg.CreateTime,
		arg.CreateTimeIso,
		arg.Input,
		arg.SearchHashtag,
		arg.SearchHashtagViews,
		arg.CommentCount,
		arg.DiggCount,
		arg.ShareCount,
		arg.PlayCount,
		arg.CollectCount,
		arg.IsAd,
		arg.IsPinned,
		arg.IsSponsored,
		arg.IsSlideshow,
		arg.ExpiresAt,
	)
	return err
}

const deleteExpiredTKVideoWithHashTags = `-- name: DeleteExpiredTKVideoWithHashTags :exec
DELETE FROM tiktok_videos_with_hashtags
WHERE expires_at < NOW()
`

func (q *Queries) DeleteExpiredTKVideoWithHashTags(ctx context.Context) error {
	_, err := q.db.Exec(ctx, deleteExpiredTKVideoWithHashTags)
	return err
}

const getTKVideoWithAuthorAndMusic = `-- name: GetTKVideoWithAuthorAndMusic :one
SELECT
    v.id, v.text, v.text_language, v.user_id, v.video_url, v.cover_url,
    v.width, v.height, v.duration, v.definition, v.create_time,
    v.create_time_iso, v.input, v.search_hashtag, v.search_hashtag_views,
    v.comment_count, v.digg_count, v.share_count, v.play_count,
    v.collect_count, v.is_ad, v.is_pinned, v.is_sponsored, v.is_slideshow,
    v.expires_at, v.created_at,

    -- Author
    a.id AS author_id, a.username, a.nickname, a.profile_url,
    a.avatar_url, a.original_avatar_url, a.signature, a.bio_link,
    a.verified, a.private_account, a.region, a.fans, a.heart,
    a.following, a.friends, a.video_count, a.digg_count AS author_digg_count,

    -- Music
    m.id AS music_id, m.name AS music_name, m.author AS music_author,
    m.is_original, m.play_url, m.cover_url AS music_cover_url,
    m.original_cover_url

FROM tiktok_videos_with_hashtags v
LEFT JOIN tiktok_authors a ON v.author_id = a.id
LEFT JOIN tiktok_music m ON v.music_id = m.id
WHERE v.id = $1
ORDER BY v.create_time DESC
`

type GetTKVideoWithAuthorAndMusicRow struct {
	ID                 string           `json:"id"`
	Text               pgtype.Text      `json:"text"`
	TextLanguage       pgtype.Text      `json:"text_language"`
	UserID             int32            `json:"user_id"`
	VideoUrl           pgtype.Text      `json:"video_url"`
	CoverUrl           pgtype.Text      `json:"cover_url"`
	Width              pgtype.Int4      `json:"width"`
	Height             pgtype.Int4      `json:"height"`
	Duration           pgtype.Int4      `json:"duration"`
	Definition         pgtype.Text      `json:"definition"`
	CreateTime         pgtype.Timestamp `json:"create_time"`
	CreateTimeIso      pgtype.Timestamp `json:"create_time_iso"`
	Input              pgtype.Text      `json:"input"`
	SearchHashtag      pgtype.Text      `json:"search_hashtag"`
	SearchHashtagViews pgtype.Int8      `json:"search_hashtag_views"`
	CommentCount       pgtype.Int4      `json:"comment_count"`
	DiggCount          pgtype.Int4      `json:"digg_count"`
	ShareCount         pgtype.Int4      `json:"share_count"`
	PlayCount          pgtype.Int8      `json:"play_count"`
	CollectCount       pgtype.Int4      `json:"collect_count"`
	IsAd               pgtype.Bool      `json:"is_ad"`
	IsPinned           pgtype.Bool      `json:"is_pinned"`
	IsSponsored        pgtype.Bool      `json:"is_sponsored"`
	IsSlideshow        pgtype.Bool      `json:"is_slideshow"`
	ExpiresAt          pgtype.Timestamp `json:"expires_at"`
	CreatedAt          pgtype.Timestamp `json:"created_at"`
	AuthorID           pgtype.Text      `json:"author_id"`
	Username           pgtype.Text      `json:"username"`
	Nickname           pgtype.Text      `json:"nickname"`
	ProfileUrl         pgtype.Text      `json:"profile_url"`
	AvatarUrl          pgtype.Text      `json:"avatar_url"`
	OriginalAvatarUrl  pgtype.Text      `json:"original_avatar_url"`
	Signature          pgtype.Text      `json:"signature"`
	BioLink            pgtype.Text      `json:"bio_link"`
	Verified           pgtype.Bool      `json:"verified"`
	PrivateAccount     pgtype.Bool      `json:"private_account"`
	Region             pgtype.Text      `json:"region"`
	Fans               pgtype.Int4      `json:"fans"`
	Heart              pgtype.Int8      `json:"heart"`
	Following          pgtype.Int4      `json:"following"`
	Friends            pgtype.Int4      `json:"friends"`
	VideoCount         pgtype.Int4      `json:"video_count"`
	AuthorDiggCount    pgtype.Int4      `json:"author_digg_count"`
	MusicID            pgtype.Text      `json:"music_id"`
	MusicName          pgtype.Text      `json:"music_name"`
	MusicAuthor        pgtype.Text      `json:"music_author"`
	IsOriginal         pgtype.Bool      `json:"is_original"`
	PlayUrl            pgtype.Text      `json:"play_url"`
	MusicCoverUrl      pgtype.Text      `json:"music_cover_url"`
	OriginalCoverUrl   pgtype.Text      `json:"original_cover_url"`
}

func (q *Queries) GetTKVideoWithAuthorAndMusic(ctx context.Context, id string) (GetTKVideoWithAuthorAndMusicRow, error) {
	row := q.db.QueryRow(ctx, getTKVideoWithAuthorAndMusic, id)
	var i GetTKVideoWithAuthorAndMusicRow
	err := row.Scan(
		&i.ID,
		&i.Text,
		&i.TextLanguage,
		&i.UserID,
		&i.VideoUrl,
		&i.CoverUrl,
		&i.Width,
		&i.Height,
		&i.Duration,
		&i.Definition,
		&i.CreateTime,
		&i.CreateTimeIso,
		&i.Input,
		&i.SearchHashtag,
		&i.SearchHashtagViews,
		&i.CommentCount,
		&i.DiggCount,
		&i.ShareCount,
		&i.PlayCount,
		&i.CollectCount,
		&i.IsAd,
		&i.IsPinned,
		&i.IsSponsored,
		&i.IsSlideshow,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.AuthorID,
		&i.Username,
		&i.Nickname,
		&i.ProfileUrl,
		&i.AvatarUrl,
		&i.OriginalAvatarUrl,
		&i.Signature,
		&i.BioLink,
		&i.Verified,
		&i.PrivateAccount,
		&i.Region,
		&i.Fans,
		&i.Heart,
		&i.Following,
		&i.Friends,
		&i.VideoCount,
		&i.AuthorDiggCount,
		&i.MusicID,
		&i.MusicName,
		&i.MusicAuthor,
		&i.IsOriginal,
		&i.PlayUrl,
		&i.MusicCoverUrl,
		&i.OriginalCoverUrl,
	)
	return i, err
}

const listTKVideoWithAuthorAndMusic = `-- name: ListTKVideoWithAuthorAndMusic :many
SELECT
    v.id, v.text, v.text_language, v.user_id, v.video_url, v.cover_url,
    v.width, v.height, v.duration, v.definition, v.create_time,
    v.create_time_iso, v.input, v.search_hashtag, v.search_hashtag_views,
    v.comment_count, v.digg_count, v.share_count, v.play_count,
    v.collect_count, v.is_ad, v.is_pinned, v.is_sponsored, v.is_slideshow,
    v.expires_at, v.created_at,

    -- Author
    a.id AS author_id, a.username, a.nickname, a.profile_url,
    a.avatar_url, a.original_avatar_url, a.signature, a.bio_link,
    a.verified, a.private_account, a.region, a.fans, a.heart,
    a.following, a.friends, a.video_count, a.digg_count AS author_digg_count,

    -- Music
    m.id AS music_id, m.name AS music_name, m.author AS music_author,
    m.is_original, m.play_url, m.cover_url AS music_cover_url,
    m.original_cover_url

FROM tiktok_videos_with_hashtags v
LEFT JOIN tiktok_authors a ON v.author_id = a.id
LEFT JOIN tiktok_music m ON v.music_id = m.id
WHERE v.user_id = $1
ORDER BY v.create_time DESC
LIMIT $2 OFFSET $3
`

type ListTKVideoWithAuthorAndMusicParams struct {
	UserID int32 `json:"user_id"`
	Limit  int32 `json:"limit"`
	Offset int32 `json:"offset"`
}

type ListTKVideoWithAuthorAndMusicRow struct {
	ID                 string           `json:"id"`
	Text               pgtype.Text      `json:"text"`
	TextLanguage       pgtype.Text      `json:"text_language"`
	UserID             int32            `json:"user_id"`
	VideoUrl           pgtype.Text      `json:"video_url"`
	CoverUrl           pgtype.Text      `json:"cover_url"`
	Width              pgtype.Int4      `json:"width"`
	Height             pgtype.Int4      `json:"height"`
	Duration           pgtype.Int4      `json:"duration"`
	Definition         pgtype.Text      `json:"definition"`
	CreateTime         pgtype.Timestamp `json:"create_time"`
	CreateTimeIso      pgtype.Timestamp `json:"create_time_iso"`
	Input              pgtype.Text      `json:"input"`
	SearchHashtag      pgtype.Text      `json:"search_hashtag"`
	SearchHashtagViews pgtype.Int8      `json:"search_hashtag_views"`
	CommentCount       pgtype.Int4      `json:"comment_count"`
	DiggCount          pgtype.Int4      `json:"digg_count"`
	ShareCount         pgtype.Int4      `json:"share_count"`
	PlayCount          pgtype.Int8      `json:"play_count"`
	CollectCount       pgtype.Int4      `json:"collect_count"`
	IsAd               pgtype.Bool      `json:"is_ad"`
	IsPinned           pgtype.Bool      `json:"is_pinned"`
	IsSponsored        pgtype.Bool      `json:"is_sponsored"`
	IsSlideshow        pgtype.Bool      `json:"is_slideshow"`
	ExpiresAt          pgtype.Timestamp `json:"expires_at"`
	CreatedAt          pgtype.Timestamp `json:"created_at"`
	AuthorID           pgtype.Text      `json:"author_id"`
	Username           pgtype.Text      `json:"username"`
	Nickname           pgtype.Text      `json:"nickname"`
	ProfileUrl         pgtype.Text      `json:"profile_url"`
	AvatarUrl          pgtype.Text      `json:"avatar_url"`
	OriginalAvatarUrl  pgtype.Text      `json:"original_avatar_url"`
	Signature          pgtype.Text      `json:"signature"`
	BioLink            pgtype.Text      `json:"bio_link"`
	Verified           pgtype.Bool      `json:"verified"`
	PrivateAccount     pgtype.Bool      `json:"private_account"`
	Region             pgtype.Text      `json:"region"`
	Fans               pgtype.Int4      `json:"fans"`
	Heart              pgtype.Int8      `json:"heart"`
	Following          pgtype.Int4      `json:"following"`
	Friends            pgtype.Int4      `json:"friends"`
	VideoCount         pgtype.Int4      `json:"video_count"`
	AuthorDiggCount    pgtype.Int4      `json:"author_digg_count"`
	MusicID            pgtype.Text      `json:"music_id"`
	MusicName          pgtype.Text      `json:"music_name"`
	MusicAuthor        pgtype.Text      `json:"music_author"`
	IsOriginal         pgtype.Bool      `json:"is_original"`
	PlayUrl            pgtype.Text      `json:"play_url"`
	MusicCoverUrl      pgtype.Text      `json:"music_cover_url"`
	OriginalCoverUrl   pgtype.Text      `json:"original_cover_url"`
}

func (q *Queries) ListTKVideoWithAuthorAndMusic(ctx context.Context, arg ListTKVideoWithAuthorAndMusicParams) ([]ListTKVideoWithAuthorAndMusicRow, error) {
	rows, err := q.db.Query(ctx, listTKVideoWithAuthorAndMusic, arg.UserID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ListTKVideoWithAuthorAndMusicRow
	for rows.Next() {
		var i ListTKVideoWithAuthorAndMusicRow
		if err := rows.Scan(
			&i.ID,
			&i.Text,
			&i.TextLanguage,
			&i.UserID,
			&i.VideoUrl,
			&i.CoverUrl,
			&i.Width,
			&i.Height,
			&i.Duration,
			&i.Definition,
			&i.CreateTime,
			&i.CreateTimeIso,
			&i.Input,
			&i.SearchHashtag,
			&i.SearchHashtagViews,
			&i.CommentCount,
			&i.DiggCount,
			&i.ShareCount,
			&i.PlayCount,
			&i.CollectCount,
			&i.IsAd,
			&i.IsPinned,
			&i.IsSponsored,
			&i.IsSlideshow,
			&i.ExpiresAt,
			&i.CreatedAt,
			&i.AuthorID,
			&i.Username,
			&i.Nickname,
			&i.ProfileUrl,
			&i.AvatarUrl,
			&i.OriginalAvatarUrl,
			&i.Signature,
			&i.BioLink,
			&i.Verified,
			&i.PrivateAccount,
			&i.Region,
			&i.Fans,
			&i.Heart,
			&i.Following,
			&i.Friends,
			&i.VideoCount,
			&i.AuthorDiggCount,
			&i.MusicID,
			&i.MusicName,
			&i.MusicAuthor,
			&i.IsOriginal,
			&i.PlayUrl,
			&i.MusicCoverUrl,
			&i.OriginalCoverUrl,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
