-- 1. Author table (unique id)
CREATE TABLE tiktok_authors (
    id TEXT PRIMARY KEY,
    username TEXT,
    nickname TEXT,
    profile_url TEXT,
    avatar_url TEXT,
    original_avatar_url TEXT,
    signature TEXT,
    bio_link TEXT,
    verified BOOLEAN,
    region TEXT,
    private_account BOOLEAN,
    fans INT,
    heart BIGINT,
    following INT,
    friends INT,
    video_count INT,
    digg_count INT,
    created_at TIMESTAMP DEFAULT NOW()
);


-- 2. Music table (unique musicId)
CREATE TABLE tiktok_music (
    id TEXT PRIMARY KEY,                             -- musicMeta.musicId
    name TEXT,                                       -- musicMeta.musicName
    author TEXT,                                     -- musicMeta.musicAuthor
    is_original BOOLEAN,           
    original_cover_url TEXT,                         -- musicMeta.musicOriginal
    play_url TEXT,                                   -- musicMeta.playUrl
    cover_url TEXT                                   -- musicMeta.coverMediumUrl
);

CREATE TABLE tiktok_videos (
    id TEXT PRIMARY KEY,                             -- Video ID
    text TEXT,                                       -- Video description
    text_language TEXT,                              -- Language
    author_id TEXT NOT NULL REFERENCES tiktok_authors(id),
    music_id TEXT REFERENCES tiktok_music(id),
    user_id INT NOT NULL REFERENCES users(id),       -- pulling user
    video_url TEXT,                                  -- webVideoUrl
    cover_url TEXT,                                  -- videoMeta.coverUrl
    width INT,                                       -- videoMeta.width
    height INT,                                      -- videoMeta.height
    duration INT,                                    -- videoMeta.duration
    definition TEXT,                                 -- videoMeta.definition
    create_time TIMESTAMP,                           -- video create_time
    comment_count INT,
    digg_count INT,
    share_count INT,
    play_count BIGINT,
    collect_count INT,
    is_ad BOOLEAN,
    is_pinned BOOLEAN,
    is_sponsored BOOLEAN,
    is_slideshow BOOLEAN,
    expires_at TIMESTAMP,                            -- Expiration time
    created_at TIMESTAMP DEFAULT NOW()               -- Insertion time
);

CREATE TABLE tiktok_video_mentions (
    video_id TEXT REFERENCES tiktok_videos(id),
    mentioned_user TEXT, 
    PRIMARY KEY (video_id, mentioned_user)
);

-- 4. Hashtag many-to-many relationship table
CREATE TABLE tiktok_video_hashtags (
    video_id TEXT REFERENCES tiktok_videos(id),
    hashtag TEXT,
    PRIMARY KEY (video_id, hashtag)
);

-- Index optimization
CREATE INDEX idx_tiktok_videos_author_id ON tiktok_videos(author_id);
CREATE INDEX idx_tiktok_videos_create_time ON tiktok_videos(create_time);
CREATE INDEX idx_tiktok_video_hashtags_tag ON tiktok_video_hashtags(hashtag);