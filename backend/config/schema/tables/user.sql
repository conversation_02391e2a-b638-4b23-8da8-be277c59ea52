CREATE TABLE users (
  id           BIGSERIAL PRIMARY KEY,
  github_id    TEXT UNIQUE,
  tiktok_id    TEXT UNIQUE,
  login        TEXT NOT NULL,
  email        TEXT,
  name         TEXT,
  avatar_url   TEXT,
  token        TEXT,
  user_type    TEXT DEFAULT 'personal' CHECK (user_type IN ('personal', 'business')),
  author_unique_ids TEXT[] DEFAULT NULL,
  daily_analysis_count INTEGER DEFAULT 0,
  last_analysis_date DATE DEFAULT CURRENT_DATE,
  created_at   TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at   TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add indexes for better performance
CREATE INDEX idx_users_user_type ON users(user_type);
CREATE INDEX idx_users_analysis_tracking ON users(last_analysis_date, daily_analysis_count);
