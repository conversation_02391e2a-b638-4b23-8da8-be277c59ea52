CREATE TABLE ai_sorted_comments (
    id SERIAL PRIMARY KEY,
    user_id INT NOT NULL,
    video_url TEXT NOT NULL,
    text TEXT,
    intent_score INT,
    labels TEXT[],
    urls TEXT[],
    final_score DOUBLE PRECISION,
    scenario_reinforcement TEXT,
    created_at TIMESTAMPTZ DEFAULT now()
);

CREATE TABLE ai_sorted_kocs (
    id SERIAL PRIMARY KEY,
    user_id INT NOT NULL,
    video_url TEXT NOT NULL,
    intent_score INT,
    labels TEXT[],
    urls TEXT[],
    final_score DOUBLE PRECISION,
    scenario_reinforcement TEXT,
    created_at TIMESTAMPTZ DEFAULT now()
);
