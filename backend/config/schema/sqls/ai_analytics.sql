-- name: InsertAISortedComment :exec
INSERT INTO ai_sorted_comments (
    video_url,user_id, text, intent_score, labels, urls, final_score, scenario_reinforcement
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8
);

-- name: InsertAISortedKoc :exec
INSERT INTO ai_sorted_kocs (
    video_url, user_id, intent_score, labels, urls, final_score, scenario_reinforcement
) VALUES (
    $1, $2, $3, $4, $5, $6, $7
);


-- name: ListAISortedComments :many
SELECT
    id,
    video_url,
    user_id,
    text,
    intent_score,
    labels,
    urls,
    final_score,
    scenario_reinforcement,
    created_at
FROM
    ai_sorted_comments
WHERE
    user_id = $1
ORDER BY
    final_score DESC
LIMIT 
    $2 OFFSET $3;


-- name: ListAISortedKocs :many
SELECT
    id,
    video_url,
    user_id,
    intent_score,
    labels,
    urls,
    final_score,
    scenario_reinforcement,
    created_at
FROM
    ai_sorted_kocs
WHERE
    user_id = $1
ORDER BY
    final_score DESC
LIMIT 
    $2 OFFSET $3;

