-- name: CreateTKComment :exec
INSERT INTO tiktok_comments (
			user_id, video_url, comment_id, comment_text,
			author_uid, author_unique_id, avatar_url, digg_count,
			liked_by_author, pinned_by_author, reply_comment_total,
			comment_time, expires_at
)
VALUES (
			$1, $2, $3, $4,
			$5, $6, $7, $8,
			$9, $10, $11,
			$12, $13
)
ON CONFLICT (comment_id) DO NOTHING;

-- name: ListTKCommentsByUserID :many
SELECT *
FROM tiktok_comments
WHERE user_id = $1
ORDER BY comment_time DESC
LIMIT $2 OFFSET $3;

-- name: CountTKCommentsByUserID :one
SELECT COUNT(*) FROM tiktok_comments WHERE user_id = $1;

-- name: ListTKCommentsByIDs :many
SELECT *
FROM tiktok_comments
WHERE comment_id = ANY($1::text[])
ORDER BY comment_time DESC;