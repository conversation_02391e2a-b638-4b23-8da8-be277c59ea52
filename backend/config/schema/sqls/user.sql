-- name: <PERSON><PERSON><PERSON>ithubUser :one
INSERT INTO users (
  github_id, login, email, name, avatar_url, token, user_type
) VALUES (
  $1, $2, $3, $4, $5, $6, $7
)
RETURNING *;

-- name: CreateTikTokUser :one
INSERT INTO users (
  tiktok_id, login, email, name, avatar_url, token, user_type
) VALUES (
  $1, $2, $3, $4, $5, $6, $7
)
RETURNING *;

-- name: GetUserByGithubID :one
SELECT * FROM users
WHERE github_id = $1;

-- name: GetUserByTikTokID :one
SELECT * FROM users
WHERE tiktok_id = $1;

-- name: UpdateUserByGithubID :one
UPDATE users
SET login = $2,
    email = $3,
    name = $4,
    avatar_url = $5,
    token = $6,
    updated_at = now()
WHERE github_id = $1
RETURNING *;

-- name: UpdateUserByTikTokID :one
UPDATE users
SET login = $2,
    email = $3,
    name = $4,
    avatar_url = $5,
    token = $6,
    updated_at = now()
WHERE tiktok_id = $1
RETURNING *;

-- name: GetUserWithID :one
SELECT * FROM users
WHERE id = $1;

-- name: GetUser :one
SELECT * FROM users
WHERE name = $1 LIMIT 1;

-- name: GetUserFromCode :one
SELECT * FROM users
WHERE token = $1 LIMIT 1;

-- name: UpdateUserAuthorUniqueIDs :one
UPDATE users
SET author_unique_ids = $2
WHERE id = $1
RETURNING *;

-- name: UpdateUserType :one
UPDATE users
SET user_type = $2,
    updated_at = now()
WHERE id = $1
RETURNING *;

-- name: GetUsersByType :many
SELECT * FROM users
WHERE user_type = $1;

-- name: IncrementAnalysisCount :one
UPDATE users
SET daily_analysis_count = CASE
    WHEN last_analysis_date = CURRENT_DATE THEN daily_analysis_count + 1
    ELSE 1
END,
last_analysis_date = CURRENT_DATE,
updated_at = now()
WHERE id = $1
RETURNING *;

-- name: GetUserAnalysisCount :one
SELECT daily_analysis_count, last_analysis_date
FROM users
WHERE id = $1;
