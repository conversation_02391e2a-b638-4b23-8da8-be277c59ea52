-- name: CreateTKVideoWithHashTags :exec
INSERT INTO tiktok_videos_with_hashtags (
    id, text, text_language, author_id, music_id, user_id,
    video_url, cover_url, width, height, duration, definition,
    create_time, create_time_iso, input, search_hashtag, search_hashtag_views,
    comment_count, digg_count, share_count, play_count, collect_count,
    is_ad, is_pinned, is_sponsored, is_slideshow, expires_at
) VALUES (
    $1, $2, $3, $4, $5, $6,
    $7, $8, $9, $10, $11, $12,
    $13, $14, $15, $16, $17,
    $18, $19, $20, $21, $22,
    $23, $24, $25, $26, $27
)
ON CONFLICT (id) DO NOTHING;


-- name: DeleteExpiredTKVideoWithHashTags :exec
DELETE FROM tiktok_videos_with_hashtags
WHERE expires_at < NOW();

-- name: CreateTKVideoEffectSticker :exec
INSERT INTO tiktok_video_effect_stickers (
    video_id, sticker_id, sticker_name
) VALUES (
    $1, $2, $3
)
ON CONFLICT (video_id, sticker_id) DO NOTHING;


-- name: ListTKVideoWithAuthorAndMusic :many
SELECT
    v.id, v.text, v.text_language, v.user_id, v.video_url, v.cover_url,
    v.width, v.height, v.duration, v.definition, v.create_time,
    v.create_time_iso, v.input, v.search_hashtag, v.search_hashtag_views,
    v.comment_count, v.digg_count, v.share_count, v.play_count,
    v.collect_count, v.is_ad, v.is_pinned, v.is_sponsored, v.is_slideshow,
    v.expires_at, v.created_at,

    -- Author
    a.id AS author_id, a.username, a.nickname, a.profile_url,
    a.avatar_url, a.original_avatar_url, a.signature, a.bio_link,
    a.verified, a.private_account, a.region, a.fans, a.heart,
    a.following, a.friends, a.video_count, a.digg_count AS author_digg_count,

    -- Music
    m.id AS music_id, m.name AS music_name, m.author AS music_author,
    m.is_original, m.play_url, m.cover_url AS music_cover_url,
    m.original_cover_url

FROM tiktok_videos_with_hashtags v
LEFT JOIN tiktok_authors a ON v.author_id = a.id
LEFT JOIN tiktok_music m ON v.music_id = m.id
WHERE v.user_id = $1
ORDER BY v.create_time DESC
LIMIT $2 OFFSET $3;

-- name: GetTKVideoWithAuthorAndMusic :one
SELECT
    v.id, v.text, v.text_language, v.user_id, v.video_url, v.cover_url,
    v.width, v.height, v.duration, v.definition, v.create_time,
    v.create_time_iso, v.input, v.search_hashtag, v.search_hashtag_views,
    v.comment_count, v.digg_count, v.share_count, v.play_count,
    v.collect_count, v.is_ad, v.is_pinned, v.is_sponsored, v.is_slideshow,
    v.expires_at, v.created_at,

    -- Author
    a.id AS author_id, a.username, a.nickname, a.profile_url,
    a.avatar_url, a.original_avatar_url, a.signature, a.bio_link,
    a.verified, a.private_account, a.region, a.fans, a.heart,
    a.following, a.friends, a.video_count, a.digg_count AS author_digg_count,

    -- Music
    m.id AS music_id, m.name AS music_name, m.author AS music_author,
    m.is_original, m.play_url, m.cover_url AS music_cover_url,
    m.original_cover_url

FROM tiktok_videos_with_hashtags v
LEFT JOIN tiktok_authors a ON v.author_id = a.id
LEFT JOIN tiktok_music m ON v.music_id = m.id
WHERE v.id = $1
ORDER BY v.create_time DESC;
