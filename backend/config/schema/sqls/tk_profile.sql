-- name: CreateTKVideoAuthor :exec
INSERT INTO tiktok_authors (
    id, username, nickname, profile_url, signature, bio_link,
    avatar_url, original_avatar_url, verified, private_account, region,
    following, friends, fans, heart, video_count, digg_count
) VALUES (
    $1, $2, $3, $4, $5, $6,
    $7, $8, $9, $10, $11,
    $12, $13, $14, $15, $16, $17
)
ON CONFLICT (id) DO NOTHING;

-- name: CreateTKVideoMusic :exec
INSERT INTO tiktok_music (
    id, name, author, is_original, play_url,
    cover_url, original_cover_url
) VALUES (
    $1, $2, $3, $4, $5,
    $6, $7
)
ON CONFLICT (id) DO NOTHING;

-- name: CreateTKVideo :exec
INSERT INTO tiktok_videos (
    id, author_id, music_id, user_id, text, text_language,
    create_time, is_ad, video_url,
    height, width, duration, cover_url,
    definition, digg_count, share_count, play_count, collect_count, comment_count,
    is_slideshow, is_pinned, is_sponsored,
    expires_at
) VALUES (
    $1, $2, $3, $4, $5, $6,
    $7, $8, $9,
    $10, $11, $12, $13,
    $14, $15, $16, $17, $18, $19,
    $20, $21, $22,
    $23
)
ON CONFLICT (id) DO NOTHING;


-- name: CreateTKVideoHashtagsAndMentions :exec
INSERT INTO tiktok_video_hashtags (video_id, hashtag) VALUES ($1, $2)
ON CONFLICT DO NOTHING;

-- name: CreateTKVideoMentions :exec
INSERT INTO tiktok_video_mentions (video_id, mentioned_user) VALUES ($1, $2)
ON CONFLICT DO NOTHING;



-- name: ListTKVideosWithDetails :many
SELECT 
    v.id AS video_id,
    v.text AS video_text,
    v.text_language,
    v.video_url,
    v.cover_url,
    v.create_time,
    v.duration,
    v.definition,
    v.comment_count,
    v.digg_count,
    v.share_count,
    v.play_count,
    v.collect_count,
    v.is_ad,
    v.is_pinned,
    v.is_sponsored,
    v.is_slideshow,
    v.user_id,
    v.expires_at,
    v.created_at,

    -- author fields
    a.id AS author_id,
    a.username AS author_username,
    a.nickname AS author_nickname,
    a.avatar_url AS author_avatar_url,
    a.signature AS author_signature,

    -- music fields
    m.id AS music_id,
    m.name AS music_name,
    m.author AS music_author,
    m.play_url AS music_play_url

FROM tiktok_videos v
LEFT JOIN tiktok_authors a ON v.author_id = a.id
LEFT JOIN tiktok_music m ON v.music_id = m.id
WHERE v.user_id = $1
ORDER BY v.create_time DESC
LIMIT $2 OFFSET $3;


-- name: ListTKAuthorsByIDs :many
SELECT *
FROM tiktok_authors
WHERE username = ANY($1::text[])
ORDER BY id;

-- name: ListTKAuthorsByAuthorIDs :many
SELECT *
FROM tiktok_videos
WHERE author_id = ANY($1::text[])
ORDER BY created_at DESC;