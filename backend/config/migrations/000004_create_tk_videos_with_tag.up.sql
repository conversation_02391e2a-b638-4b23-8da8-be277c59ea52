-- Create main TikTok video table with author/music relationships
CREATE TABLE tiktok_videos_with_hashtags (
    id TEXT PRIMARY KEY,                             -- Video ID
    text TEXT,                                       -- Video description
    text_language TEXT,                              -- Language
    author_id TEXT NOT NULL REFERENCES tiktok_authors(id),
    music_id TEXT REFERENCES tiktok_music(id),
    user_id INT NOT NULL REFERENCES users(id),       -- pulling user
    video_url TEXT,                                  -- webVideoUrl
    cover_url TEXT,                                  -- videoMeta.coverUrl
    width INT,                                       -- videoMeta.width
    height INT,                                      -- videoMeta.height
    duration INT,                                    -- videoMeta.duration
    definition TEXT,                                 -- videoMeta.definition
    create_time TIMESTAMP,                           -- video create_time
    comment_count INT,
    digg_count INT,
    share_count INT,
    play_count BIGINT,
    collect_count INT,      
    create_time_iso TIMESTAMP,      
    input TEXT,                     
    search_hashtag TEXT,           
    search_hashtag_views BIGINT, 
    is_ad BOOLEAN,
    is_pinned BOOLEAN,
    is_sponsored BOOLEAN,
    is_slideshow BOOLEAN,
    expires_at TIMESTAMP,                            -- Expiration time
    created_at TIMESTAMP DEFAULT NOW()               -- Insertion time
);


-- Create effect stickers relation table
CREATE TABLE tiktok_video_effect_stickers (
    video_id TEXT REFERENCES tiktok_videos(id),
    sticker_id TEXT,
    sticker_name TEXT,
    PRIMARY KEY (video_id, sticker_id)
);
