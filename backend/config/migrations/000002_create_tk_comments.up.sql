CREATE TABLE tiktok_comments (
    id SERIAL PRIMARY KEY,
    user_id INT NOT NULL,                               -- ID of the user who initiated the scraping (custom app field)
    video_url TEXT NOT NULL,                            -- TikTok video URL (from "videoWebUrl" or "submittedVideoUrl")
    comment_id TEXT NOT NULL UNIQUE,                           -- Comment ID (from "cid")
    comment_text TEXT NOT NULL,                         -- Comment content (from "text")
    author_uid TEXT,                                     -- Author's UID (from "uid")
    author_unique_id TEXT,                              -- Author's username (from "uniqueId")
    avatar_url TEXT,                                     -- Author's avatar thumbnail URL (from "avatarThumbnail")
    digg_count INT DEFAULT 0,                            -- Number of likes on comment (from "diggCount")
    liked_by_author <PERSON><PERSON><PERSON><PERSON><PERSON>,                             -- Whether the comment is liked by the author (from "likedByAuthor")
    pinned_by_author <PERSON><PERSON><PERSON><PERSON><PERSON>,                            -- Whether the comment is pinned (from "pinnedBy<PERSON>uth<PERSON>")
    reply_comment_total INT,                             -- Number of replies to this comment (from "replyCommentTotal")
    created_at TIMESTAMP DEFAULT NOW(),                 -- Record insertion time (custom, not from API)
    comment_time TIMESTAMP,                              -- Original comment time (from "createTime" as UNIX timestamp)
    expires_at TIMESTAMP                                 -- Auto-deletion time (custom: created_at + 7 days)
);

-- Indexes to optimize filtering and sorting
CREATE INDEX idx_tiktok_comments_user_id ON tiktok_comments(user_id);
CREATE INDEX idx_tiktok_comments_comment_time ON tiktok_comments(comment_time);
