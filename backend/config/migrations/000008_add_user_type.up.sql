-- Add user_type column to users table
ALTER TABLE users ADD COLUMN user_type TEXT DEFAULT 'personal' CHECK (user_type IN ('personal', 'business'));

-- Add index for user_type for better query performance
CREATE INDEX idx_users_user_type ON users(user_type);

-- Add analysis_requests_count for rate limiting
ALTER TABLE users ADD COLUMN daily_analysis_count INTEGER DEFAULT 0;
ALTER TABLE users ADD COLUMN last_analysis_date DATE DEFAULT CURRENT_DATE;

-- Create index for analysis tracking
CREATE INDEX idx_users_analysis_tracking ON users(last_analysis_date, daily_analysis_count);
