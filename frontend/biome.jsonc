{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "ignore": []}, "formatter": {"enabled": true}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"nursery": {"useSortedClasses": {"level": "warn", "fix": "safe", "options": {"functions": ["clsx", "cva", "cn"]}}}, "recommended": true}}}