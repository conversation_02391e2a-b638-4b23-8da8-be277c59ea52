"use client";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/animate-ui/components/tabs";
import { useTikTokUser } from "@/hooks/useGithubUser";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import FetchTkVideoWithHashTags from "./components/fetchTKVideoWithHashTags";
import FetchTkComments from "./components/fetchTKComments";
import FetchTkProfile from "./components/fetchTKProfile";


export default function DashboardPage() {
    const { loading, error } = useTikTokUser();
    const router = useRouter();

    useEffect(() => {
        if (error) {
            router.push("/login");
        }
    }, [error, router]);

    if (loading) {
        return <div className="text-white">Loading...</div>;
    }


    return (
        <Tabs defaultValue="comments" className="custom-a w-full  bg-[#262626] text-white rounded-lg ">
            <TabsList className="grid w-full grid-cols-3 bg-[#262626]">
                <TabsTrigger value="comments">Comments</TabsTrigger>
                <TabsTrigger value="profile">Profile</TabsTrigger>
                <TabsTrigger value="tags">Tags</TabsTrigger>
            </TabsList>

            <TabsContents className="mx-1 mb-1 -mt-2 rounded-sm h-full bg-black">
                <TabsContent value="comments" className="space-y-6 p-2">
                    <FetchTkComments />
                </TabsContent>
                <TabsContent value="profile" className="space-y-6 p-6">
                    <FetchTkProfile />
                </TabsContent>
                <TabsContent value="tags" className="space-y-6 p-6">
                    <FetchTkVideoWithHashTags />
                </TabsContent>
            </TabsContents>
        </Tabs>

    )

}