import { InputButton, InputButtonAction, InputButtonInput, InputButtonProvider, InputButtonSubmit } from "@/components/animate-ui/buttons/input";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCaption,
    TableCell,
    TableFooter,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table"
import { tkCommentsService, tkProfileService, tkVideoWithHashTagsService } from "@/utils/api";
import { AbbreviatedText } from "@/utils/tools";
import type { TKComment, TKProfile, TKVideoWithHashTags } from "@/utils/type";
import { Check, Loader2 } from "lucide-react";
import { motion } from "motion/react";
import { useCallback, useEffect, useState, useTransition } from "react";
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination"
import { Button } from "@/components/ui/button";
import { CopyButton } from "@/components/animate-ui/buttons/copy";
import toast from "react-hot-toast";

export default function FetchTkVideoWithHashTags() {
    const [filter, setFilter] = useState("All");
    const [currentPage, setCurrentPage] = useState(1);
    const [profilePerPost, setProfilePerPost] = useState('1');
    const pageSize = 3;

    const [data, setData] = useState<TKVideoWithHashTags[]>([]);
    const [totalPages, setTotalPages] = useState(1);
    const [loading, setLoading] = useState(false);
    const [showInput, setShowInput] = useState(false);
    const [pending, startTransition] = useTransition();
    const [success, setSuccess] = useState(false);
    const [value, setValue] = useState('');


    const handlePrev = () => {
        if (currentPage > 1) setCurrentPage((p) => p - 1);
    };
    const handleNext = () => {
        if (currentPage < totalPages) setCurrentPage((p) => p + 1);
    };

    const getPageNumbers = useCallback(() => {
        const pages = [];
        const maxVisiblePages = 5;
        const half = Math.floor(maxVisiblePages / 2);

        let start = Math.max(1, currentPage - half);
        let end = Math.min(totalPages, start + maxVisiblePages - 1);

        if (end - start + 1 < maxVisiblePages) {
            start = Math.max(1, end - maxVisiblePages + 1);
        }

        for (let i = start; i <= end; i++) {
            pages.push(i);
        }
        return pages;
    }, [currentPage, totalPages]);

    const handlePageChange = (page: number) => {
        if (page >= 1 && page <= totalPages) {
            setCurrentPage(page);
        }
    };



    const handleSubmit = useCallback(
        (e: React.FormEvent<HTMLFormElement>) => {
            e.preventDefault();
            const req = {
                "excludePinnedPosts": false,
                "hashtags": [value],
                "resultsPerPage": Number(profilePerPost) || 1,
                "scrapeRelatedVideos": false,
                "shouldDownloadAvatars": false,
                "shouldDownloadCovers": false,
                "shouldDownloadMusicCovers": false,
                "shouldDownloadSlideshowImages": false,
                "shouldDownloadSubtitles": false,
                "shouldDownloadVideos": false,
                "profileScrapeSections": ["videos"],
                "profileSorting": "latest",
                "searchSection": "",
                "maxProfilesPerQuery": 10
            }

            setLoading(true);
            startTransition(async () => {
                try {
                    const res = await tkVideoWithHashTagsService.post<{ data: TKVideoWithHashTags[] }>(
                        "",
                        req,
                        "application/json"
                    );
                    setData(res.data);
                    toast.success(`Videos fetched successfully! Total videos: ${res.data.length}`);
                    setSuccess(true);
                    setValue('');
                } catch (error) {
                    toast.error("Failed to fetch videos. Please check the tag and try again.");
                    console.error("API request error:", error);
                } finally {
                    setLoading(false);
                }
            });
        },
        [showInput, setShowInput, setSuccess, setValue, value, profilePerPost, setLoading],
    );


    const TKCommentsTable = useCallback(({ data }: { data: TKVideoWithHashTags[] }) => {
        return (
            <Table className="my-2">
                <TableCaption>List of TikTok posts</TableCaption>
                <TableHeader className="[&_th]:text-white bg-[#262626]">
                    <TableRow className="text-white">
                        <TableHead>User</TableHead>
                        <TableHead>Text</TableHead>
                        <TableHead>Hashtags</TableHead>
                        <TableHead>Likes</TableHead>
                        <TableHead>Plays</TableHead>
                        <TableHead>Video</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {data?.map((item) => (
                        <TableRow key={item.id}>
                            <TableCell className="flex items-center gap-2">
                                <img
                                    src={item.authorMeta?.avatar}
                                    alt={item.authorMeta?.nickName}
                                    className="w-8 h-8 rounded-full"
                                />
                                <span>{item.authorMeta?.nickName}</span>
                            </TableCell>
                            <TableCell>{AbbreviatedText({ text: item.text, maxLength: 20 })}<CopyButton content={item.text} size="sm" /></TableCell>
                            <TableCell>
                                {item.hashtags.map((tag) => tag.name).join(', ') || 'None'}
                            </TableCell>
                            <TableCell>{item.diggCount}</TableCell>
                            <TableCell>{item.playCount}</TableCell>
                            <TableCell>
                                <a
                                    href={item.webVideoUrl}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-blue-600 underline"
                                >
                                    Link
                                </a>
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        );
    }, [data]);

    return (
        <div className="flex flex-col items-center justify-center h-full p-2">
            <h1 className="text-2xl font-bold mb-2">Fetch TikTok Video With Tags</h1>
            <div className="flex flex-col items-center justify-center space-y-4 mb-2 w-full">
                <form
                    className="flex flex-col items-center justify-center space-y-4 mb-2 w-full"
                    onSubmit={(e) => {
                        handleSubmit(e);
                    }}>

                    <div className="flex items-center space-x-2">
                        <div className="w-28 text-center">Search Count:</div>
                        <Input className="w-20" type="text" onChange={(e) => setProfilePerPost(e.target.value)} placeholder="Count" />
                    </div>
                    <div className="flex items-center space-x-2">
                        <div className="w-28 text-center">Search Tag:</div>
                        <Input className="w-98" type="text" onChange={(e) => setValue(e.target.value)} placeholder="Tag" />
                    </div>

                    <Button type="submit" className="bg-white cursor-pointer text-black hover:bg-[#d1d1d1]">Submit</Button>
                </form>
            </div>


            {loading ? (
                <div className="flex justify-center items-center">
                    <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
                </div>
            ) : (
                <div>{data && data.length > 0 &&
                    <div className="container flex flex-col items-center justify-center gap-12 px-4 scrollbar-white">
                        <div className="max-h-[500px] overflow-y-auto w-full scrollbar-white">
                            <h1>Total: {data.length}</h1>

                            <TKCommentsTable data={data} />
                        </div>
                    </div>
                }</div>


            )}

            {/* Pagination */}
            {data?.length > 0 && <Pagination className="mt-6 cursor-pointer">
                <PaginationContent>
                    <PaginationItem>
                        <PaginationPrevious
                            onClick={() => handlePageChange(currentPage - 1)}
                            className={
                                currentPage === 1
                                    ? "pointer-events-none opacity-50"
                                    : "hover:bg-gray-100"
                            }
                        />
                    </PaginationItem>
                    {getPageNumbers().map((page) => (
                        <PaginationItem key={page}>
                            <PaginationLink
                                onClick={() => handlePageChange(page)}
                                isActive={page === currentPage}
                                className={
                                    page === currentPage
                                        ? "bg-blue-600 text-white hover:bg-blue-700"
                                        : "hover:bg-gray-100"
                                }
                            >
                                {page}
                            </PaginationLink>
                        </PaginationItem>
                    ))}
                    {totalPages > 5 && currentPage < totalPages - 2 && (
                        <PaginationItem>
                            <PaginationEllipsis />
                        </PaginationItem>
                    )}
                    <PaginationItem>
                        <PaginationNext
                            onClick={() => handlePageChange(currentPage + 1)}
                            className={
                                currentPage === totalPages
                                    ? "pointer-events-none opacity-50"
                                    : "hover:bg-gray-100"
                            }
                        />
                    </PaginationItem>
                </PaginationContent>
            </Pagination>}
        </div>
    );
}