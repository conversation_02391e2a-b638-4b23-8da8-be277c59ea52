import { CopyButton } from "@/components/animate-ui/buttons/copy";
import { InputButton, InputButtonAction, InputButtonInput, InputButtonProvider, InputButtonSubmit } from "@/components/animate-ui/buttons/input";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCaption,
    TableCell,
    TableFooter,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table"
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination"
import { tkCommentsService } from "@/utils/api";
import { AbbreviatedText } from "@/utils/tools";
import type { TKComment } from "@/utils/type";
import { Check, Loader2 } from "lucide-react";
import { motion } from "motion/react";
import { useCallback, useState, useTransition } from "react";
import toast from "react-hot-toast";


export default function FetchTkComments() {
    const [filter, setFilter] = useState("All");
    const [currentPage, setCurrentPage] = useState(1);
    const [commentsPerPost, setCommentsPerPost] = useState('1');

    const [data, setData] = useState<TKComment[]>([]);
    const [totalPages, setTotalPages] = useState(1);
    const [loading, setLoading] = useState(false);
    const [showInput, setShowInput] = useState(false);
    const [pending, startTransition] = useTransition();
    const [success, setSuccess] = useState(false);
    const [value, setValue] = useState('');

    const getPageNumbers = useCallback(() => {
        const pages = [];
        const maxVisiblePages = 5;
        const half = Math.floor(maxVisiblePages / 2);

        let start = Math.max(1, currentPage - half);
        let end = Math.min(totalPages, start + maxVisiblePages - 1);

        if (end - start + 1 < maxVisiblePages) {
            start = Math.max(1, end - maxVisiblePages + 1);
        }

        for (let i = start; i <= end; i++) {
            pages.push(i);
        }
        return pages;
    }, [currentPage, totalPages]);

    const handlePageChange = (page: number) => {
        if (page >= 1 && page <= totalPages) {
            setCurrentPage(page);
        }
    };

    const handleSubmit = useCallback(
        (e: React.FormEvent<HTMLFormElement>) => {
            e.preventDefault();
            const req = {
                commentsPerPost: Number(commentsPerPost) || 1,
                "excludePinnedPosts": false,
                "maxRepliesPerComment": 0,
                "postURLs": [
                    value
                ],
                "resultsPerPage": 100
            }

            if (!showInput) {
                setShowInput(true);
                return;
            }
            setLoading(true);
            startTransition(async () => {
                try {
                    const res = await tkCommentsService.post<{ data: TKComment[] }>(
                        "",
                        req,
                        "application/json"
                    );
                    setData(res.data);
                    setSuccess(true);
                    const ttt = data.length > 0 ? `Total comments: ${res.data.length}` : "No comments found."
                    toast.success("Comments fetched successfully! " + ttt);
                    setValue('');
                } catch (error) {
                    toast.error("Failed to fetch comments. Please check the URL and try again.");
                    console.error("API request error:", error);
                } finally {
                    setLoading(false);
                }
            });
        },
        [showInput, setShowInput, setSuccess, setValue, value, commentsPerPost],
    );

    const TKCommentsTable = useCallback(({ data }: { data: TKComment[] }) => {
        return (
            <Table className="my-2">
                <TableCaption>List of TikTok comments</TableCaption>
                <TableHeader className="[&_th]:text-white bg-[#262626]">
                    <TableRow className="text-white">
                        <TableHead>User</TableHead>
                        <TableHead>Comment</TableHead>
                        <TableHead>Likes</TableHead>
                        <TableHead>Video</TableHead>
                        <TableHead>Created At</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {data?.map((item) => (
                        <TableRow key={item.cid}>
                            <TableCell className="flex items-center gap-2">
                                <img
                                    src={item.avatarThumbnail}
                                    alt={item.uniqueId}
                                    className="w-8 h-8 rounded-full"
                                />
                                <span>{item.uniqueId}</span>
                            </TableCell>
                            <TableCell>{AbbreviatedText({ text: item.text, maxLength: 80 })}<CopyButton content={item.text} size="sm" /></TableCell>
                            <TableCell>{item.diggCount}</TableCell>
                            <TableCell>
                                <a
                                    href={item.videoWebUrl}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-blue-600 underline"
                                >
                                    Link
                                </a>
                            </TableCell>
                            <TableCell>
                                {new Date(item.createTimeISO).toLocaleString()}
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        );
    }, [data]);

    return (
        <div className="flex flex-col items-center justify-center h-full p-2">
            <h1 className="text-2xl font-bold mb-2">Fetch TikTok Comments</h1>
            <div className="flex flex-col items-center justify-center space-y-4 mb-2 w-full">
                <div className="flex items-center space-x-2">
                    <div className="w-28 text-center">Search Count:</div>
                    <Input className="w-20" type="email" onChange={(e) => setCommentsPerPost(e.target.value)} placeholder="Count" />
                </div>
                <form
                    onSubmit={handleSubmit}
                    className="flex items-center justify-center w-full"
                >
                    <InputButtonProvider className="text-black" showInput={showInput} setShowInput={setShowInput}>
                        <InputButton>
                            <InputButtonAction onClick={() => { }}>
                                input the url
                            </InputButtonAction>
                            <InputButtonSubmit
                                onClick={() => { }}
                                type="submit"
                                disabled={pending}
                                className={pending || success ? 'aspect-square px-0' : ''}
                            >
                                {success ? (
                                    <motion.span
                                        key="success"
                                        initial={{ opacity: 0, scale: 0 }}
                                        animate={{ opacity: 1, scale: 1 }}
                                        transition={{ duration: 0.2 }}
                                    >
                                        <Check />
                                    </motion.span>
                                ) : pending ? (
                                    <motion.span
                                        key="pending"
                                        initial={{ opacity: 0, scale: 0 }}
                                        animate={{ opacity: 1, scale: 1 }}
                                        transition={{ duration: 0.2 }}
                                    >
                                        <Loader2 className="animate-spin" />
                                    </motion.span>
                                ) : (
                                    'Submit'
                                )}
                            </InputButtonSubmit>
                        </InputButton>
                        <InputButtonInput
                            type="text"
                            placeholder="video url"
                            value={value}
                            onChange={(e) => setValue(e.target.value)}
                            disabled={pending}
                            autoFocus
                        />
                    </InputButtonProvider>
                </form>
            </div>


            {loading ? (
                <div className="flex justify-center items-center">
                    <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
                </div>
            ) : (

                <div>{data && data.length > 0 &&
                    <div className="container flex flex-col items-center justify-center gap-12 px-4 scrollbar-white">
                        <div className="max-h-[500px] overflow-y-auto w-full scrollbar-white">
                            <h1>Total: {data.length}</h1>

                            <TKCommentsTable data={data} />
                        </div>
                    </div>
                }</div>


            )}

            {/* Pagination */}
            {data?.length > 0 && <Pagination className="mt-6 cursor-pointer">
                <PaginationContent>
                    <PaginationItem>
                        <PaginationPrevious
                            onClick={() => handlePageChange(currentPage - 1)}
                            className={
                                currentPage === 1
                                    ? "pointer-events-none opacity-50"
                                    : "hover:bg-gray-100"
                            }
                        />
                    </PaginationItem>
                    {getPageNumbers().map((page) => (
                        <PaginationItem key={page}>
                            <PaginationLink
                                onClick={() => handlePageChange(page)}
                                isActive={page === currentPage}
                                className={
                                    page === currentPage
                                        ? "bg-blue-600 text-white hover:bg-blue-700"
                                        : "hover:bg-gray-100"
                                }
                            >
                                {page}
                            </PaginationLink>
                        </PaginationItem>
                    ))}
                    {totalPages > 5 && currentPage < totalPages - 2 && (
                        <PaginationItem>
                            <PaginationEllipsis />
                        </PaginationItem>
                    )}
                    <PaginationItem>
                        <PaginationNext
                            onClick={() => handlePageChange(currentPage + 1)}
                            className={
                                currentPage === totalPages
                                    ? "pointer-events-none opacity-50"
                                    : "hover:bg-gray-100"
                            }
                        />
                    </PaginationItem>
                </PaginationContent>
            </Pagination>}
        </div>
    );
}