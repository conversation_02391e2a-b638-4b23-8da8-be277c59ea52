"use client";

import Link from "next/link";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>rigger,
    <PERSON><PERSON><PERSON>ontent,
    TabsContents,
} from '@/components/animate-ui/components/tabs';
import { Label } from '@/components/ui/label';
import SignIn from "@/components/sign-in";
import UserTypeSelector from "@/components/user-type-selector";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { UserType } from "@/utils/type";

export default function HomePage() {
    const router = useRouter();
    const [selectedUserType, setSelectedUserType] = useState<UserType | null>(null);
    const [showLogin, setShowLogin] = useState(false);

    useEffect(() => {
        const url = new URL(window.location.href);
        const code = url.searchParams.get('code');
        const state = url.searchParams.get('state');
        const userType = url.searchParams.get('user_type');

        if (code && state) {
            document.cookie = `tiktok_code=${code}; path=/;`;
            document.cookie = `tiktok_state=${state}; path=/;`;

            if (userType) {
                document.cookie = `tiktok_user_type=${userType}; path=/;`;
            }

            router.push('/dashboard');
        }
    }, [router]);

    const handleUserTypeSelect = (userType: UserType) => {
        setSelectedUserType(userType);
        setShowLogin(true);
    };

    const handleBackToUserTypeSelection = () => {
        setShowLogin(false);
        setSelectedUserType(null);
    };

    if (!showLogin) {
        return (
            <UserTypeSelector
                onSelect={handleUserTypeSelect}
                isLoading={false}
            />
        );
    }

    return (
        <div className="min-h-screen bg-black flex items-center justify-center p-4">
            <div className="w-full max-w-md">
                <div className="mb-6 text-center">
                    <Button
                        variant="outline"
                        onClick={handleBackToUserTypeSelection}
                        className="mb-4 text-white border-gray-600 hover:bg-gray-800"
                    >
                        ← 返回选择用户类型
                    </Button>
                    <h2 className="text-2xl font-bold text-white mb-2">
                        {selectedUserType === 'personal' ? '个人用户登录' : '电商主登录'}
                    </h2>
                    <p className="text-gray-400">
                        使用TikTok账户登录以开始分析
                    </p>
                </div>

                <Tabs defaultValue="account" className="w-full bg-[#262626] text-white rounded-lg">
                    <TabsList className="grid w-full grid-cols-2 bg-[#262626]">
                        <TabsTrigger value="account">Login</TabsTrigger>
                        <TabsTrigger value="password">Sign Up</TabsTrigger>
                    </TabsList>

                    <TabsContents className="mx-1 mb-1 -mt-2 rounded-sm h-full bg-black">
                        <TabsContent value="account" className="space-y-6 p-6">
                            <SignIn userType={selectedUserType} />
                        </TabsContent>
                        <TabsContent value="password" className="space-y-6 p-6">
                            <SignIn userType={selectedUserType} />
                        </TabsContent>
                    </TabsContents>
                </Tabs>
            </div>
        </div>
    );
}
