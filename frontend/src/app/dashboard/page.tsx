"use client";
import { useTikTokUser } from "@/hooks/useGithubUser";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import PersonalDashboard from "./components/PersonalDashboard";
import BusinessDashboard from "./components/BusinessDashboard";
import {
    Dialog,
    DialogBackdrop,
    DialogPanel,
    DialogTitle,
    DialogDescription,
    DialogHeader,
    DialogFooter,
} from '@/components/animate-ui/headless/dialog';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { tkAuthorIDsService } from "@/utils/api";
import toast from "react-hot-toast";
import type { UserType } from "@/utils/type";

export default function DashboardPage() {
    const { loading, error, userInfo } = useTikTokUser();
    const router = useRouter();
    const [accounts, setAccounts] = useState<string[]>([]);
    const [input, setInput] = useState("");
    const [toDelete, setToDelete] = useState<string | null>(null);
    const [isOpen, setIsOpen] = useState(false);
    const [userType, setUserType] = useState<UserType>('personal');


    useEffect(() => {
        if (error) {
            router.push("/login");
        }
    }, [error, router]);

    useEffect(() => {
        if (userInfo?.author_unique_ids) {
            setAccounts(userInfo.author_unique_ids);
        }

        // 检测用户类型
        if (userInfo?.user_type) {
            setUserType(userInfo.user_type);
        } else {
            const userTypeCookie = document.cookie
                .split('; ')
                .find(row => row.startsWith('tiktok_user_type='))
                ?.split('=')[1];

            if (userTypeCookie && (userTypeCookie === 'personal' || userTypeCookie === 'business')) {
                setUserType(userTypeCookie as UserType);
            }
        }
    }, [userInfo]);

    if (loading) {
        return <div className="text-white">Loading...</div>;
    }

    const handleAdd = async () => {
        const val = input.trim();
        if (!val) {
            toast.error("Empty input!");
            return
        }
        if (accounts.includes(val)) {
            toast.error("Already has!");
            return
        }
        const newAccounts = [...accounts, val];
        const x = await tkAuthorIDsService.post('', { authorIds: newAccounts })
        toast.success("Successful")
        setAccounts(newAccounts);
        setInput("");
    };

    const handleDelete = async () => {
        if (toDelete) {
            const newAccounts = accounts.filter(acc => acc !== toDelete);
            setAccounts(newAccounts);
            await tkAuthorIDsService.post('', { authorIds: newAccounts });
            setToDelete(null);
            setIsOpen(false);
            toast.success("Successfully deleted!");
        }
    };


    const handleAccountsChange = (newAccounts: string[]) => {
        setAccounts(newAccounts);
    };

    return (
        <div className="min-h-screen bg-black text-white p-6 mt-20 w-full">
            {/* 根据用户类型显示不同的仪表板 */}
            {userType === 'business' ? (
                <BusinessDashboard
                    userInfo={userInfo}
                    accounts={accounts}
                    onAccountsChange={handleAccountsChange}
                />
            ) : (
                <PersonalDashboard
                    userInfo={userInfo}
                    accounts={accounts}
                    onAccountsChange={handleAccountsChange}
                />
            )}

            {/* 保留原有的账户管理功能作为通用组件 */}
            <div className="mt-8 bg-gray-900 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-white mb-4">账户管理<span className="text-gray-500"> (选填)</span></h3>
                <div className="flex-col space-y-4 gap-3 items-center justify-start w-full mb-4">
                    <div className="flex items-center gap-2">
                        <Input
                            value={input}
                            onChange={e => setInput(e.target.value)}
                            placeholder="输入您的TikTok账户ID"
                            className="w-60 text-white bg-gray-800 border-gray-600"
                        />
                        <Button onClick={handleAdd} className="bg-white cursor-pointer text-black hover:bg-[#d1d1d1]">添加</Button>
                    </div>
                    <div className="flex gap-2 flex-wrap">
                        {accounts.map(acc => (
                            <span key={acc} className="bg-blue-600 text-white px-3 py-1 rounded-full flex items-center relative">
                                {acc}
                                <button
                                    className="ml-2 text-xs cursor-pointer text-white bg-red-500 rounded-full w-5 h-5 flex items-center border justify-center absolute -top-2 -right-2"
                                    onClick={() => { setToDelete(acc); setIsOpen(true); }}
                                >×</button>
                            </span>
                        ))}
                    </div>
                </div>
            </div>

            {/* Modal Confirm */}
            <Dialog open={isOpen} onClose={() => setIsOpen(false)}>
                <DialogBackdrop />

                <DialogPanel className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>确认删除</DialogTitle>
                        <DialogDescription>
                            您确定要删除账户 <b>{toDelete}</b> 吗？
                        </DialogDescription>
                    </DialogHeader>

                    <div className="grid gap-4 py-4">
                        <p>
                            此操作无法撤销
                        </p>
                    </div>

                    <DialogFooter className="cursor-pointer">
                        <Button className="cursor-pointer" variant="outline" onClick={() => setIsOpen(false)}>
                            取消
                        </Button>
                        <Button
                            className="cursor-pointer"
                            type="submit"
                            variant="destructive"
                            onClick={handleDelete}
                        >
                            确认删除
                        </Button>
                    </DialogFooter>
                </DialogPanel>
            </Dialog>
        </div>
    );
}