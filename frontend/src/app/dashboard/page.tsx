"use client";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/animate-ui/components/tabs";
import { useTikTokUser } from "@/hooks/useGithubUser";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import ListTkComments from "./components/listTKComments";
import ListTkProfile from "./components/listTKProfile";
import ListTkVideoWithHashTags from "./components/listTKVideoWithHashTags";
import {
    Dialog,
    DialogBackdrop,
    DialogPanel,
    DialogTitle,
    DialogDescription,
    DialogHeader,
    DialogFooter,
} from '@/components/animate-ui/headless/dialog';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { tkAuthorIDsService } from "@/utils/api";
import toast from "react-hot-toast";

export default function DashboardPage() {
    const { loading, error, userInfo } = useTikTokUser();
    const router = useRouter();
    const [accounts, setAccounts] = useState<string[]>([]);
    const [input, setInput] = useState("");
    const [toDelete, setToDelete] = useState<string | null>(null);
    const [isOpen, setIsOpen] = useState(false);



    useEffect(() => {
        if (error) {

            router.push("/login");
        }
    }, [error, router]);

    useEffect(() => {
        if (userInfo?.author_unique_ids) {
            setAccounts(userInfo.author_unique_ids);
        }
    }, [userInfo]);

    if (loading) {
        return <div className="text-white">Loading...</div>;
    }

    const handleAdd = async () => {
        const val = input.trim();
        if (!val) {
            toast.error("Empty input!");
            return
        }
        if (accounts.includes(val)) {
            toast.error("Already has!");
            return
        }
        const newAccounts = [...accounts, val];
        const x = await tkAuthorIDsService.post('', { authorIds: newAccounts })
        console.log(x);
        toast.success("Successful")
        setAccounts(newAccounts);
        setInput("");
    };

    const handleDelete = async () => {
        if (toDelete) {
            const newAccounts = accounts.filter(acc => acc !== toDelete);
            setAccounts(newAccounts);
            await tkAuthorIDsService.post('', { authorIds: newAccounts });
            setToDelete(null);
            setIsOpen(false);
            toast.success("Successfully deleted!");
        }
    };


    return (

        <div className="my-32 w-full ">
            {/* Account input and display */}
            <div className="flex-col space-y-4 gap-3 items-center justify-start w-full mb-4 ">
                <div className="flex items-center gap-2">
                    <Input
                        value={input}
                        onChange={e => setInput(e.target.value)}
                        placeholder="Enter your Tiktok account ID"
                        className="w-60 text-white"
                    />

                    <Button onClick={handleAdd} className="bg-white cursor-pointer text-black hover:bg-[#d1d1d1]">ADD</Button>

                </div>
                <div className="flex gap-2 flex-wrap">
                    {accounts.map(acc => (
                        <span key={acc} className="bg-blue-600 text-white px-3 py-1 rounded-full flex items-center relative">
                            {acc}
                            <button
                                className="ml-2 text-xs cursor-pointer text-white bg-red-500 rounded-full w-5 h-5 flex items-center border justify-center absolute -top-2 -right-2"
                                onClick={() => { setToDelete(acc); setIsOpen(true); }}
                            >×</button>
                        </span>
                    ))}
                </div>
            </div>

            {/* Modal Confirm */}
            <Dialog open={isOpen} onClose={() => setIsOpen(false)}>
                <DialogBackdrop />

                <DialogPanel className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>Confirm deletion</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to delete account <b>{toDelete}</b> ?
                        </DialogDescription>
                    </DialogHeader>

                    <div className="grid gap-4 py-4">
                        <p>
                            Delete confirm
                        </p>
                    </div>

                    <DialogFooter className="cursor-pointer">
                        <Button className="cursor-pointer" variant="outline" onClick={() => setIsOpen(false)}>
                            Cancel
                        </Button>
                        <Button
                            className="cursor-pointer"
                            type="submit"
                            variant="destructive"
                            onClick={handleDelete}
                        >
                            Delete confirm
                        </Button>
                    </DialogFooter>
                </DialogPanel>
            </Dialog>
            <Tabs defaultValue="comments" className="custom-a  bg-[#262626] text-white rounded-lg">
                <TabsList className="grid w-full grid-cols-3 bg-[#262626]">
                    <TabsTrigger value="comments">Comments</TabsTrigger>
                    <TabsTrigger value="profile">Profile</TabsTrigger>
                    <TabsTrigger value="tags">Tags</TabsTrigger>
                </TabsList>

                <TabsContents className="mx-1 mb-1 -mt-2 rounded-sm h-full bg-black">
                    <TabsContent value="comments" className="space-y-6 p-6">
                        <ListTkComments />
                    </TabsContent>
                    <TabsContent value="profile" className="space-y-6 p-6">
                        <ListTkProfile />
                    </TabsContent>
                    <TabsContent value="tags" className="space-y-6 p-6">
                        <ListTkVideoWithHashTags />
                    </TabsContent>
                </TabsContents>
            </Tabs>
        </div>
    )

}