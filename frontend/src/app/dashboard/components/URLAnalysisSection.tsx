"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
    Search,
    Link,
    BarChart3,
    TrendingUp,
    Table as TableIcon,
    LayoutGrid
} from "lucide-react";
import type { TKComment, UserType } from "@/utils/type";
import { tkAnalyticsService, tkCommentsService } from "@/utils/api";
import toast from "react-hot-toast";

interface URLAnalysisSectionProps {
    urlInput: string;
    setUrlInput: (url: string) => void;
    userType: UserType;
}

export default function URLAnalysisSection({
    urlInput,
    setUrlInput,
    userType
}: URLAnalysisSectionProps) {
    const [isAnalyzing, setIsAnalyzing] = useState(false);
    const [analysisResults, setAnalysisResults] = useState<any>(null);
    const [description, setDescription] = useState("");
    const [viewMode, setViewMode] = useState<'table' | 'card'>('card');
    const [data, setData] = useState<TKComment[]>([]);

    const handleAnalyze = async () => {
        if (!urlInput.trim()) return;

        setIsAnalyzing(true);
        try {
            // 根据用户类型调用不同的API端点
            const endpoint = userType === 'business'
                ? '/api/v1/analysis/business'
                : '/api/v1/analysis/personal';

            // const response = await fetch(process.env.NEXT_PUBLIC_API_BASE_URL + endpoint, {
            //     method: 'POST',
            //     headers: {
            //         'Content-Type': 'application/json',
            //     },
            //     credentials: 'include',
            //     body: JSON.stringify({
            //         url: urlInput.trim(),
            //         description: description.trim()
            //     })
            // });
            if (userType === 'business') {
                const req = {
                    commentsPerPost: 30,
                    "excludePinnedPosts": false,
                    "maxRepliesPerComment": 0,
                    "postURLs": [
                        urlInput.trim()
                    ],
                    "resultsPerPage": 100
                }
                const res = await tkCommentsService.post<{ data: TKComment[] }>(
                    "",
                    req,
                    "application/json"
                );
                setData(res.data);

                const ttt = data.length > 0 ? `Total comments: ${res.data.length}` : "No comments found."
                toast.success("Comments fetched successfully! " + ttt);
                setAnalysisResults(results);
            } else {
                const results = await tkAnalyticsService.post("", {
                    url: urlInput.trim(),
                    description: description.trim()
                });
                // if (!response.ok) {
                //     throw new Error(`HTTP error! status: ${response.status}`);
                // }

                // const results = await response.json();
                setAnalysisResults(results);
            }
        } catch (error) {
            console.error("Analysis failed:", error);
            // 如果API调用失败，显示模拟数据
            const mockResults = {
                basic: {
                    title: "冬日穿搭分享 | 温暖又时尚的搭配技巧",
                    views: "125.6K",
                    likes: "8.9K",
                    comments: "456",
                    shares: "234"
                },
                engagement: {
                    rate: "7.2%",
                    sentiment: "积极",
                    topKeywords: ["冬装", "搭配", "时尚", "保暖"]
                },
                business: userType === 'business' ? {
                    conversionPotential: "高",
                    targetAudience: "25-35岁女性",
                    recommendedActions: [
                        "增加产品链接",
                        "优化标题关键词",
                        "添加购买引导"
                    ]
                } : null
            };
            setAnalysisResults(mockResults);
        } finally {
            setIsAnalyzing(false);
        }
    };

    return (
        <div className="space-y-6">
            {/* 输入区域 */}
            <Card className="bg-gray-900 border-gray-700">
                <CardHeader>
                    <CardTitle className="text-white flex items-center space-x-2">
                        <Link className="h-5 w-5" />
                        <span>TikTok链接分析</span>
                    </CardTitle>
                    <CardDescription className="text-gray-400">
                        {userType === 'personal'
                            ? "输入TikTok视频链接和描述，获取详细的内容分析和优化建议"
                            : "输入TikTok视频链接和描述，获取商业价值分析和营销建议"
                        }
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    {/* URL输入框 */}
                    <div>
                        <label className="text-white text-sm font-medium mb-2 block">TikTok链接 *</label>
                        <Input
                            placeholder="粘贴TikTok视频链接..."
                            value={urlInput}
                            onChange={(e) => setUrlInput(e.target.value)}
                            className="bg-gray-800 border-gray-600 text-white placeholder-gray-400"
                        />
                    </div>

                    {/* 描述输入框 */}
                    <div>
                        <label className="text-white text-sm font-medium mb-2 block">分析描述</label>
                        <Textarea
                            placeholder="请描述您想要分析的内容或关注的重点..."
                            value={description}
                            onChange={(e) => setDescription(e.target.value)}
                            className="bg-gray-800 border-gray-600 text-white placeholder-gray-400 min-h-[100px]"
                        />
                    </div>

                    {/* 分析按钮 */}
                    <Button
                        onClick={handleAnalyze}
                        disabled={!urlInput.trim() || isAnalyzing}
                        className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                    >
                        {isAnalyzing ? (
                            <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                分析中...
                            </>
                        ) : (
                            <>
                                <Search className="h-4 w-4 mr-2" />
                                开始分析
                            </>
                        )}
                    </Button>
                </CardContent>
            </Card>

            {/* 分析结果展示 */}
            {analysisResults && (
                <div className="space-y-6">
                    {/* 视图切换按钮 */}
                    <div className="flex items-center justify-between">
                        <h3 className="text-xl font-semibold text-white">分析结果</h3>
                        <div className="flex items-center space-x-2">
                            <Button
                                variant={viewMode === 'card' ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => setViewMode('card')}
                                className="text-white"
                            >
                                <LayoutGrid className="h-4 w-4 mr-2" />
                                卡片视图
                            </Button>
                            <Button
                                variant={viewMode === 'table' ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => setViewMode('table')}
                                className="text-white"
                            >
                                <TableIcon className="h-4 w-4 mr-2" />
                                表格视图
                            </Button>
                        </div>
                    </div>

                    {/* 卡片视图 */}
                    {viewMode === 'card' && (
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            {/* 基础数据卡片 */}
                            <Card className="bg-gray-900 border-gray-700">
                                <CardHeader>
                                    <CardTitle className="text-white flex items-center space-x-2">
                                        <BarChart3 className="h-5 w-5" />
                                        <span>基础数据</span>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        <div>
                                            <p className="text-gray-400 text-sm">视频标题</p>
                                            <p className="text-white text-sm">{analysisResults.basic?.title}</p>
                                        </div>
                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <p className="text-gray-400 text-sm">播放量</p>
                                                <p className="text-white font-semibold">{analysisResults.basic?.views}</p>
                                            </div>
                                            <div>
                                                <p className="text-gray-400 text-sm">点赞数</p>
                                                <p className="text-white font-semibold">{analysisResults.basic?.likes}</p>
                                            </div>
                                            <div>
                                                <p className="text-gray-400 text-sm">评论数</p>
                                                <p className="text-white font-semibold">{analysisResults.basic?.comments}</p>
                                            </div>
                                            <div>
                                                <p className="text-gray-400 text-sm">分享数</p>
                                                <p className="text-white font-semibold">{analysisResults.basic?.shares}</p>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* 互动分析卡片 */}
                            <Card className="bg-gray-900 border-gray-700">
                                <CardHeader>
                                    <CardTitle className="text-white flex items-center space-x-2">
                                        <TrendingUp className="h-5 w-5" />
                                        <span>互动分析</span>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        <div className="flex justify-between items-center">
                                            <span className="text-gray-400">互动率</span>
                                            <span className="text-green-400 font-semibold">{analysisResults.engagement?.rate}</span>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-gray-400">情感倾向</span>
                                            <span className="text-blue-400">{analysisResults.engagement?.sentiment}</span>
                                        </div>
                                        <div>
                                            <p className="text-gray-400 text-sm mb-2">热门关键词</p>
                                            <div className="flex flex-wrap gap-2">
                                                {analysisResults.engagement?.topKeywords?.map((keyword: string, index: number) => (
                                                    <span
                                                        key={index}
                                                        className="bg-blue-600 text-white px-2 py-1 rounded-full text-xs"
                                                    >
                                                        {keyword}
                                                    </span>
                                                ))}
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* 商业分析（仅电商主可见） */}
                            {userType === 'business' && analysisResults.business && (
                                <Card className="bg-gray-900 border-gray-700 lg:col-span-2">
                                    <CardHeader>
                                        <CardTitle className="text-white">商业价值分析</CardTitle>
                                        <CardDescription className="text-gray-400">
                                            基于内容的商业潜力评估
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                            <div>
                                                <p className="text-gray-400 text-sm">转化潜力</p>
                                                <p className={`font-semibold ${analysisResults.business.conversionPotential === '高'
                                                    ? 'text-green-400'
                                                    : 'text-yellow-400'
                                                    }`}>
                                                    {analysisResults.business.conversionPotential}
                                                </p>
                                            </div>
                                            <div>
                                                <p className="text-gray-400 text-sm">目标受众</p>
                                                <p className="text-white">{analysisResults.business.targetAudience}</p>
                                            </div>
                                            <div>
                                                <p className="text-gray-400 text-sm mb-2">优化建议</p>
                                                <div className="space-y-1">
                                                    {analysisResults.business.recommendedActions.map((action: string, index: number) => (
                                                        <div key={index} className="flex items-center space-x-2">
                                                            <div className="w-1.5 h-1.5 bg-green-400 rounded-full"></div>
                                                            <span className="text-white text-xs">{action}</span>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            )}
                        </div>
                    )}

                    {/* 表格视图 */}
                    {viewMode === 'table' && (
                        <Card className="bg-gray-900 border-gray-700">
                            <CardHeader>
                                <CardTitle className="text-white">分析数据表格</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="overflow-x-auto">
                                    <table className="w-full text-white">
                                        <thead className="bg-gray-800">
                                            <tr>
                                                <th className="px-4 py-2 text-left">指标</th>
                                                <th className="px-4 py-2 text-left">数值</th>
                                                <th className="px-4 py-2 text-left">说明</th>
                                            </tr>
                                        </thead>
                                        <tbody className="divide-y divide-gray-700">
                                            <tr>
                                                <td className="px-4 py-3 text-gray-300">视频标题</td>
                                                <td className="px-4 py-3" colSpan={2}>{analysisResults.basic?.title}</td>
                                            </tr>
                                            <tr>
                                                <td className="px-4 py-3 text-gray-300">播放量</td>
                                                <td className="px-4 py-3 font-semibold">{analysisResults.basic?.views}</td>
                                                <td className="px-4 py-3 text-gray-400">视频总播放次数</td>
                                            </tr>
                                            <tr>
                                                <td className="px-4 py-3 text-gray-300">点赞数</td>
                                                <td className="px-4 py-3 font-semibold">{analysisResults.basic?.likes}</td>
                                                <td className="px-4 py-3 text-gray-400">视频获得的点赞总数</td>
                                            </tr>
                                            <tr>
                                                <td className="px-4 py-3 text-gray-300">评论数</td>
                                                <td className="px-4 py-3 font-semibold">{analysisResults.basic?.comments}</td>
                                                <td className="px-4 py-3 text-gray-400">视频获得的评论总数</td>
                                            </tr>
                                            <tr>
                                                <td className="px-4 py-3 text-gray-300">分享数</td>
                                                <td className="px-4 py-3 font-semibold">{analysisResults.basic?.shares}</td>
                                                <td className="px-4 py-3 text-gray-400">视频被分享的次数</td>
                                            </tr>
                                            <tr>
                                                <td className="px-4 py-3 text-gray-300">互动率</td>
                                                <td className="px-4 py-3 text-green-400 font-semibold">{analysisResults.engagement?.rate}</td>
                                                <td className="px-4 py-3 text-gray-400">互动数与播放量的比率</td>
                                            </tr>
                                            <tr>
                                                <td className="px-4 py-3 text-gray-300">情感倾向</td>
                                                <td className="px-4 py-3 text-blue-400">{analysisResults.engagement?.sentiment}</td>
                                                <td className="px-4 py-3 text-gray-400">评论的整体情感分析</td>
                                            </tr>
                                            {userType === 'business' && analysisResults.business && (
                                                <>
                                                    <tr>
                                                        <td className="px-4 py-3 text-gray-300">转化潜力</td>
                                                        <td className="px-4 py-3 text-green-400 font-semibold">{analysisResults.business.conversionPotential}</td>
                                                        <td className="px-4 py-3 text-gray-400">内容转化为销售的潜力评估</td>
                                                    </tr>
                                                    <tr>
                                                        <td className="px-4 py-3 text-gray-300">目标受众</td>
                                                        <td className="px-4 py-3">{analysisResults.business.targetAudience}</td>
                                                        <td className="px-4 py-3 text-gray-400">最适合的目标用户群体</td>
                                                    </tr>
                                                </>
                                            )}
                                        </tbody>
                                    </table>
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </div>
            )}
        </div>
    );
}
