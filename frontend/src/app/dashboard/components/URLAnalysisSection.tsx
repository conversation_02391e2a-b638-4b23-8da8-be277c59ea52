"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Search, Link, BarChart3, TrendingUp, TextCursorInput } from "lucide-react";
import type { UserType } from "@/utils/type";

interface URLAnalysisSectionProps {
    urlInput: string;
    setUrlInput: (url: string) => void;
    onAnalyze: () => void;
    userType: UserType;
}

export default function URLAnalysisSection({
    urlInput,
    setUrlInput,
    onAnalyze,
    userType
}: URLAnalysisSectionProps) {
    const [isAnalyzing, setIsAnalyzing] = useState(false);
    const [analysisResults, setAnalysisResults] = useState<any>(null);

    const handleAnalyze = async () => {
        if (!urlInput.trim()) return;

        setIsAnalyzing(true);
        try {
            // TODO: 调用实际的分析API
            await new Promise(resolve => setTimeout(resolve, 2000)); // 模拟API调用

            // 模拟分析结果
            const mockResults = {
                basic: {
                    title: "冬日穿搭分享 | 温暖又时尚的搭配技巧",
                    views: "125.6K",
                    likes: "8.9K",
                    comments: "456",
                    shares: "234"
                },
                engagement: {
                    rate: "7.2%",
                    sentiment: "积极",
                    topKeywords: ["冬装", "搭配", "时尚", "保暖"]
                },
                business: userType === 'business' ? {
                    conversionPotential: "高",
                    targetAudience: "25-35岁女性",
                    recommendedActions: [
                        "增加产品链接",
                        "优化标题关键词",
                        "添加购买引导"
                    ]
                } : null
            };

            setAnalysisResults(mockResults);
        } catch (error) {
            console.error("Analysis failed:", error);
        } finally {
            setIsAnalyzing(false);
        }
    };

    return (
        <div className="space-y-6">
            <Card className="bg-gray-900 border-gray-700">
                <CardHeader>
                    <CardTitle className="text-white flex items-center space-x-2">
                        <TextCursorInput className="h-5 w-5" />
                        <span>描述</span>
                    </CardTitle>
                </CardHeader>
                <CardContent>

                </CardContent>
            </Card>
            {/* URL输入区域 */}
            <Card className="bg-gray-900 border-gray-700">
                <CardHeader>
                    <CardTitle className="text-white flex items-center space-x-2">
                        <Link className="h-5 w-5" />
                        <span>TikTok链接分析</span>
                    </CardTitle>
                    <CardDescription className="text-gray-400">
                        {userType === 'personal'
                            ? "输入TikTok视频链接，获取详细的内容分析和优化建议"
                            : "输入TikTok视频链接，获取商业价值分析和营销建议"
                        }
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="flex space-x-2">
                        <Input
                            placeholder="粘贴TikTok视频链接..."
                            value={urlInput}
                            onChange={(e) => setUrlInput(e.target.value)}
                            className="flex-1 bg-gray-800 border-gray-600 text-white placeholder-gray-400"
                        />
                        <Button
                            onClick={handleAnalyze}
                            disabled={!urlInput.trim() || isAnalyzing}
                            className="bg-blue-600 hover:bg-blue-700 text-white"
                        >
                            {isAnalyzing ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                    分析中...
                                </>
                            ) : (
                                <>
                                    <Search className="h-4 w-4 mr-2" />
                                    分析
                                </>
                            )}
                        </Button>
                    </div>
                </CardContent>
            </Card>

            {/* 分析结果展示 */}
            {analysisResults && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* 基础数据 */}
                    <Card className="bg-gray-900 border-gray-700">
                        <CardHeader>
                            <CardTitle className="text-white flex items-center space-x-2">
                                <BarChart3 className="h-5 w-5" />
                                <span>基础数据</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                <div>
                                    <p className="text-gray-400 text-sm">视频标题</p>
                                    <p className="text-white text-sm">{analysisResults.basic.title}</p>
                                </div>
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <p className="text-gray-400 text-sm">播放量</p>
                                        <p className="text-white font-semibold">{analysisResults.basic.views}</p>
                                    </div>
                                    <div>
                                        <p className="text-gray-400 text-sm">点赞数</p>
                                        <p className="text-white font-semibold">{analysisResults.basic.likes}</p>
                                    </div>
                                    <div>
                                        <p className="text-gray-400 text-sm">评论数</p>
                                        <p className="text-white font-semibold">{analysisResults.basic.comments}</p>
                                    </div>
                                    <div>
                                        <p className="text-gray-400 text-sm">分享数</p>
                                        <p className="text-white font-semibold">{analysisResults.basic.shares}</p>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* 互动分析 */}
                    <Card className="bg-gray-900 border-gray-700">
                        <CardHeader>
                            <CardTitle className="text-white flex items-center space-x-2">
                                <TrendingUp className="h-5 w-5" />
                                <span>互动分析</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                <div className="flex justify-between items-center">
                                    <span className="text-gray-400">互动率</span>
                                    <span className="text-green-400 font-semibold">{analysisResults.engagement.rate}</span>
                                </div>
                                <div className="flex justify-between items-center">
                                    <span className="text-gray-400">情感倾向</span>
                                    <span className="text-blue-400">{analysisResults.engagement.sentiment}</span>
                                </div>
                                <div>
                                    <p className="text-gray-400 text-sm mb-2">热门关键词</p>
                                    <div className="flex flex-wrap gap-2">
                                        {analysisResults.engagement.topKeywords.map((keyword: string, index: number) => (
                                            <span
                                                key={index}
                                                className="bg-blue-600 text-white px-2 py-1 rounded-full text-xs"
                                            >
                                                {keyword}
                                            </span>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* 商业分析（仅电商主可见） */}
                    {userType === 'business' && analysisResults.business && (
                        <Card className="bg-gray-900 border-gray-700 lg:col-span-2">
                            <CardHeader>
                                <CardTitle className="text-white">商业价值分析</CardTitle>
                                <CardDescription className="text-gray-400">
                                    基于内容的商业潜力评估
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div>
                                        <p className="text-gray-400 text-sm">转化潜力</p>
                                        <p className={`font-semibold ${analysisResults.business.conversionPotential === '高'
                                            ? 'text-green-400'
                                            : 'text-yellow-400'
                                            }`}>
                                            {analysisResults.business.conversionPotential}
                                        </p>
                                    </div>
                                    <div>
                                        <p className="text-gray-400 text-sm">目标受众</p>
                                        <p className="text-white">{analysisResults.business.targetAudience}</p>
                                    </div>
                                    <div>
                                        <p className="text-gray-400 text-sm mb-2">优化建议</p>
                                        <div className="space-y-1">
                                            {analysisResults.business.recommendedActions.map((action: string, index: number) => (
                                                <div key={index} className="flex items-center space-x-2">
                                                    <div className="w-1.5 h-1.5 bg-green-400 rounded-full"></div>
                                                    <span className="text-white text-xs">{action}</span>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </div>
            )}
        </div>
    );
}
