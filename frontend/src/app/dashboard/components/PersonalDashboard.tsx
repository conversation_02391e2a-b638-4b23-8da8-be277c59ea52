"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/animate-ui/components/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { BarChart3, TrendingUp, Users, Heart, MessageCircle, Share } from "lucide-react";
import ListTkComments from "./listTKComments";
import ListTkProfile from "./listTKProfile";
import ListTkVideoWithHashTags from "./listTKVideoWithHashTags";
import URLAnalysisSection from "./URLAnalysisSection";

interface PersonalDashboardProps {
    userInfo: any;
    accounts: string[];
    onAccountsChange: (accounts: string[]) => void;
}

export default function PersonalDashboard({ userInfo, accounts, onAccountsChange }: PersonalDashboardProps) {
    const [urlInput, setUrlInput] = useState("");

    const personalMetrics = [
        {
            title: "总粉丝数",
            value: "12.5K",
            change: "+2.3%",
            icon: Users,
            color: "text-blue-400"
        },
        {
            title: "平均点赞数",
            value: "856",
            change: "+5.1%",
            icon: Heart,
            color: "text-red-400"
        },
        {
            title: "评论互动率",
            value: "4.2%",
            change: "+0.8%",
            icon: MessageCircle,
            color: "text-green-400"
        },
        {
            title: "分享次数",
            value: "234",
            change: "+12.5%",
            icon: Share,
            color: "text-purple-400"
        }
    ];

    const handleAnalyzeURL = () => {
        if (!urlInput.trim()) return;
        
        // TODO: 实现URL分析逻辑
        console.log("Analyzing URL:", urlInput);
    };

    return (
        <div className="space-y-6">
            {/* 个人用户欢迎区域 */}
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
                <h1 className="text-2xl font-bold mb-2">欢迎回来，{userInfo?.name || '创作者'}！</h1>
                <p className="text-blue-100">分析您的内容表现，获取个性化的创作建议</p>
            </div>

            {/* 个人指标卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {personalMetrics.map((metric, index) => {
                    const Icon = metric.icon;
                    return (
                        <Card key={index} className="bg-gray-900 border-gray-700">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-300">
                                    {metric.title}
                                </CardTitle>
                                <Icon className={`h-4 w-4 ${metric.color}`} />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-white">{metric.value}</div>
                                <p className="text-xs text-green-400">
                                    {metric.change} 相比上周
                                </p>
                            </CardContent>
                        </Card>
                    );
                })}
            </div>

            {/* URL分析区域 */}
            <URLAnalysisSection 
                urlInput={urlInput}
                setUrlInput={setUrlInput}
                onAnalyze={handleAnalyzeURL}
                userType="personal"
            />

            {/* 个人用户专属功能标签页 */}
            <Tabs defaultValue="content" className="bg-[#262626] text-white rounded-lg">
                <TabsList className="grid w-full grid-cols-4 bg-[#262626]">
                    <TabsTrigger value="content">内容分析</TabsTrigger>
                    <TabsTrigger value="audience">粉丝洞察</TabsTrigger>
                    <TabsTrigger value="trends">趋势追踪</TabsTrigger>
                    <TabsTrigger value="suggestions">创作建议</TabsTrigger>
                </TabsList>

                <TabsContents className="mx-1 mb-1 -mt-2 rounded-sm h-full bg-black">
                    <TabsContent value="content" className="space-y-6 p-6">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <Card className="bg-gray-900 border-gray-700">
                                <CardHeader>
                                    <CardTitle className="text-white">最佳发布时间</CardTitle>
                                    <CardDescription className="text-gray-400">
                                        基于您的粉丝活跃度分析
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-2">
                                        <div className="flex justify-between text-white">
                                            <span>周一-周三</span>
                                            <span className="text-green-400">19:00-21:00</span>
                                        </div>
                                        <div className="flex justify-between text-white">
                                            <span>周四-周日</span>
                                            <span className="text-green-400">20:00-22:00</span>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card className="bg-gray-900 border-gray-700">
                                <CardHeader>
                                    <CardTitle className="text-white">热门标签推荐</CardTitle>
                                    <CardDescription className="text-gray-400">
                                        适合您内容风格的标签
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="flex flex-wrap gap-2">
                                        {['#生活分享', '#日常vlog', '#美食探店', '#旅行记录'].map((tag) => (
                                            <span key={tag} className="bg-blue-600 text-white px-2 py-1 rounded-full text-xs">
                                                {tag}
                                            </span>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>

                    <TabsContent value="audience" className="space-y-6 p-6">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <Card className="bg-gray-900 border-gray-700">
                                <CardHeader>
                                    <CardTitle className="text-white">粉丝年龄分布</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        <div className="flex justify-between items-center">
                                            <span className="text-gray-300">18-24岁</span>
                                            <div className="flex items-center space-x-2">
                                                <div className="w-20 bg-gray-700 rounded-full h-2">
                                                    <div className="bg-blue-400 h-2 rounded-full" style={{width: '45%'}}></div>
                                                </div>
                                                <span className="text-white text-sm">45%</span>
                                            </div>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-gray-300">25-34岁</span>
                                            <div className="flex items-center space-x-2">
                                                <div className="w-20 bg-gray-700 rounded-full h-2">
                                                    <div className="bg-green-400 h-2 rounded-full" style={{width: '35%'}}></div>
                                                </div>
                                                <span className="text-white text-sm">35%</span>
                                            </div>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-gray-300">35+岁</span>
                                            <div className="flex items-center space-x-2">
                                                <div className="w-20 bg-gray-700 rounded-full h-2">
                                                    <div className="bg-purple-400 h-2 rounded-full" style={{width: '20%'}}></div>
                                                </div>
                                                <span className="text-white text-sm">20%</span>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card className="bg-gray-900 border-gray-700">
                                <CardHeader>
                                    <CardTitle className="text-white">地域分布</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-2">
                                        {[
                                            { city: '北京', percentage: 25 },
                                            { city: '上海', percentage: 20 },
                                            { city: '广州', percentage: 15 },
                                            { city: '深圳', percentage: 12 },
                                            { city: '其他', percentage: 28 }
                                        ].map((item) => (
                                            <div key={item.city} className="flex justify-between text-white">
                                                <span>{item.city}</span>
                                                <span className="text-blue-400">{item.percentage}%</span>
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>

                    <TabsContent value="trends" className="space-y-6 p-6">
                        <Card className="bg-gray-900 border-gray-700">
                            <CardHeader>
                                <CardTitle className="text-white">当前热门趋势</CardTitle>
                                <CardDescription className="text-gray-400">
                                    适合您参与的热门话题
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {[
                                        { trend: '新年计划分享', heat: '🔥🔥🔥', engagement: '高' },
                                        { trend: '冬日穿搭', heat: '🔥🔥', engagement: '中' },
                                        { trend: '年终总结', heat: '🔥🔥🔥', engagement: '高' },
                                        { trend: '美食制作', heat: '🔥', engagement: '中' }
                                    ].map((item, index) => (
                                        <div key={index} className="flex justify-between items-center p-3 bg-gray-800 rounded-lg">
                                            <div>
                                                <span className="text-white font-medium">{item.trend}</span>
                                                <span className="text-gray-400 ml-2">{item.heat}</span>
                                            </div>
                                            <span className={`px-2 py-1 rounded text-xs ${
                                                item.engagement === '高' ? 'bg-green-600 text-white' : 'bg-yellow-600 text-white'
                                            }`}>
                                                {item.engagement}互动
                                            </span>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="suggestions" className="space-y-6 p-6">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <Card className="bg-gray-900 border-gray-700">
                                <CardHeader>
                                    <CardTitle className="text-white">AI创作建议</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        <div className="p-3 bg-blue-900/30 border border-blue-700 rounded-lg">
                                            <p className="text-blue-300 text-sm">💡 建议增加更多互动元素，如问答、投票等</p>
                                        </div>
                                        <div className="p-3 bg-green-900/30 border border-green-700 rounded-lg">
                                            <p className="text-green-300 text-sm">🎯 您的美食内容表现最佳，建议继续深耕</p>
                                        </div>
                                        <div className="p-3 bg-purple-900/30 border border-purple-700 rounded-lg">
                                            <p className="text-purple-300 text-sm">📈 尝试在19-21点发布，互动率更高</p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card className="bg-gray-900 border-gray-700">
                                <CardHeader>
                                    <CardTitle className="text-white">内容优化建议</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        <div className="flex items-start space-x-3">
                                            <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2"></div>
                                            <div>
                                                <p className="text-white text-sm font-medium">视频时长优化</p>
                                                <p className="text-gray-400 text-xs">建议控制在15-30秒，完播率更高</p>
                                            </div>
                                        </div>
                                        <div className="flex items-start space-x-3">
                                            <div className="w-2 h-2 bg-blue-400 rounded-full mt-2"></div>
                                            <div>
                                                <p className="text-white text-sm font-medium">封面优化</p>
                                                <p className="text-gray-400 text-xs">使用明亮色彩和清晰文字</p>
                                            </div>
                                        </div>
                                        <div className="flex items-start space-x-3">
                                            <div className="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
                                            <div>
                                                <p className="text-white text-sm font-medium">标题优化</p>
                                                <p className="text-gray-400 text-xs">加入疑问句和情感词汇</p>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>
                </TabsContents>
            </Tabs>
        </div>
    );
}
