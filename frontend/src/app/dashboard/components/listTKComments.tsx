import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    Table,
    TableBody,
    TableCaption,
    TableCell,
    TableFooter,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table"
import { tkAIAgentService, tkCommentsService } from "@/utils/api";
import type { AnalyticsComments, AnalyticsKocs, TKCommentFromAPI } from "@/utils/type";
import { useCallback, useEffect, useRef, useState } from "react";

import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination"
import {
    Dialog,
    DialogBackdrop,
    DialogPanel,
    DialogTitle,
    DialogDescription,
    DialogHeader,
    DialogFooter,
} from '@/components/animate-ui/headless/dialog';
import { AbbreviatedText } from "@/utils/tools";
import { CopyButton } from "@/components/animate-ui/buttons/copy";
import { Loader2 } from "lucide-react";
import toast from "react-hot-toast";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { TKAnalyticsCommentsTable } from "@/app/analytics/components/analyticsComments";
import { Tabs, TabsContent, TabsContents, TabsList, TabsTrigger } from "@/components/animate-ui/components/tabs";
import ListTkProfile from "./listTKProfile";
import { TKAnalyticsKocsTable } from "@/app/analytics/components/analyticsKocs";


interface TKCommentsResponse {
    data: TKCommentFromAPI[];
    total: number,
    page: number,
    page_size: number,
}

export default function ListTkComments() {
    const [filter, setFilter] = useState("All");
    const [currentPage, setCurrentPage] = useState(1);
    const [description, setDescription] = useState(""); // Added state for description
    const pageSize = 20;
    const textareaRef = useRef<HTMLTextAreaElement>(null); // Added ref for textarea
    const [isOpen, setIsOpen] = useState(false);
    const [analyticsComments, setAnalyticsComments] = useState<AnalyticsComments[]>([]);
    const [analyticsKocs, setAnalyticsKocs] = useState<AnalyticsKocs[]>([]);

    const [data, setData] = useState<TKCommentFromAPI[]>([]);
    const [totalPages, setTotalPages] = useState(1);
    const [loading, setLoading] = useState(false);
    const [selectedPosts, setSelectedPosts] = useState<string[]>([]); // Track selected post IDs
    const [isSubmitting, setIsSubmitting] = useState(false); // Track submission state

    const fetchComments = useCallback(async () => {
        setLoading(true);

        try {
            const params: Record<string, any> = {
                page: currentPage,
                page_size: pageSize,
            };

            if (filter !== "All") {
                params.status = filter;
            }

            const res = await tkCommentsService.get<TKCommentsResponse>(
                "",
                "application/json",
                { params }
            );

            setData(res.data);
            setTotalPages(Math.ceil(res.total / pageSize));
        } catch (error) {
            console.error("Failed to fetch comments:", error);
        } finally {
            setLoading(false);
        }
    }, [filter, currentPage]);

    useEffect(() => {
        fetchComments();
    }, [filter, currentPage]);


    const getPageNumbers = useCallback(() => {
        const pages = [];
        const maxVisiblePages = 5;
        const half = Math.floor(maxVisiblePages / 2);

        let start = Math.max(1, currentPage - half);
        let end = Math.min(totalPages, start + maxVisiblePages - 1);

        if (end - start + 1 < maxVisiblePages) {
            start = Math.max(1, end - maxVisiblePages + 1);
        }

        for (let i = start; i <= end; i++) {
            pages.push(i);
        }
        return pages;
    }, [currentPage, totalPages]);

    const handlePageChange = (page: number) => {
        if (page >= 1 && page <= totalPages) {
            setCurrentPage(page);
        }
    };

    const handleCheckboxChange = (postId: string) => {
        setSelectedPosts((prev) =>
            prev.includes(postId)
                ? prev.filter((id) => id !== postId)
                : [...prev, postId]
        );
    };

    // Handle sending selected posts to API
    const handleSendSelectedPosts = useCallback(async () => {
        if (selectedPosts.length === 0) {
            toast.error('Please select at least one post.');
            return;
        }
        if (description === "") {
            toast.error('Please enter a description.');
            return;
        }

        setIsSubmitting(true);
        try {
            const response = await tkAIAgentService.post<{ data: { sortedComments: AnalyticsComments[], sortedKocs: AnalyticsKocs[] } }>(
                '',
                { description, commentIds: selectedPosts, profileVideoIDs: [], videoHashTags: [] },
            );
            setAnalyticsComments(response.data.sortedComments)
            setAnalyticsKocs(response.data.sortedKocs)
            toast.success('Selected posts sent successfully!');
            setIsOpen(true);
            setSelectedPosts([]);
        } catch (error) {
            console.error('Error sending selected posts:', error);
            toast.error('Failed to send selected posts.');
        } finally {
            setIsSubmitting(false);
        }
    }, [selectedPosts, description, setAnalyticsComments, setAnalyticsKocs]);

    const handleDescriptionChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
        setDescription(e.target.value);
        if (textareaRef.current) {
            textareaRef.current.focus();
        }
    }, []);

    const handleDelete = async () => {
        setIsOpen(false);
    };

    const TKCommentsTable = useCallback(({ data }: { data: TKCommentFromAPI[] }) => {
        return (
            <div>
                <Table className="my-2">
                    <TableCaption>List of TikTok comments</TableCaption>
                    <TableHeader className="[&_th]:text-white bg-[#262626]">
                        <TableRow>
                            <TableHead className="w-[50px]">
                                <Checkbox
                                    id="select-all"
                                    checked={selectedPosts?.length === data?.length && data?.length > 0}
                                    onCheckedChange={() =>
                                        setSelectedPosts(
                                            selectedPosts?.length === data?.length
                                                ? []
                                                : data.map((item) => item.comment_id)
                                        )
                                    }
                                />
                            </TableHead>
                            <TableHead>User</TableHead>
                            <TableHead>Comment</TableHead>
                            <TableHead>Likes</TableHead>
                            <TableHead>Video</TableHead>
                            <TableHead>Created At</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {data?.map((item) => (
                            <TableRow key={item.id}>
                                <TableCell>
                                    <Checkbox
                                        id={`toggle-${item.comment_id}`}
                                        checked={selectedPosts.includes(item.comment_id)}
                                        onCheckedChange={() => handleCheckboxChange(item.comment_id)}
                                    />
                                </TableCell>
                                <TableCell className="flex items-center gap-2">
                                    <img
                                        src={item.avatar_url}
                                        alt={item.author_unique_id}
                                        className="w-8 h-8 rounded-full"
                                    />
                                    <span>{item.author_unique_id}</span>
                                </TableCell>
                                <TableCell>{AbbreviatedText({ text: item.comment_text, maxLength: 80 })}<CopyButton content={item.comment_text} size="sm" /></TableCell>
                                <TableCell>{item.digg_count}</TableCell>
                                <TableCell>
                                    <a
                                        href={item.video_url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-blue-600 underline"
                                    >
                                        Link
                                    </a>
                                </TableCell>
                                <TableCell>
                                    {new Date(item.created_at).toLocaleString()}
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>

        );
    }, [data, selectedPosts]);



    return (
        <div className="flex flex-col items-center justify-center h-full p-2">
            <h1 className="text-2xl font-bold mb-4">TikTok Comments</h1>

            <Dialog open={isOpen} onClose={() => setIsOpen(false)}>
                <DialogBackdrop />

                <DialogPanel className="sm:max-w-full max-h-[90vh] flex flex-col">
                    <DialogHeader>
                        <DialogTitle className="my-4">AI analysis results</DialogTitle>
                    </DialogHeader>

                    <div className="flex-grow overflow-y-auto">
                        <Tabs
                            defaultValue="sortedComments"
                            className="custom-a bg-[#262626] text-white rounded-lg"
                        >
                            <TabsList className="grid w-full grid-cols-2 bg-[#262626]">
                                <TabsTrigger value="sortedComments">sortedComments</TabsTrigger>
                                <TabsTrigger value="sortedKocs">sortedKocs</TabsTrigger>
                            </TabsList>

                            <TabsContents className="mx-1 mb-1 -mt-2 rounded-sm h-full bg-black">
                                <TabsContent value="sortedComments" className="space-y-6 p-6">

                                    <div className="max-h-[500px] overflow-y-auto">
                                        <TKAnalyticsCommentsTable data={analyticsComments} />
                                    </div>

                                </TabsContent>

                                <TabsContent value="sortedKocs" className="space-y-6 p-6">

                                    <div className="max-h-[500px] overflow-y-auto">
                                        <TKAnalyticsKocsTable data={analyticsKocs} />
                                    </div>

                                </TabsContent>
                            </TabsContents>
                        </Tabs>
                    </div>


                    <DialogFooter className="cursor-pointer">
                        <Button className="cursor-pointer" variant="outline" onClick={() => setIsOpen(false)}>
                            Cancel
                        </Button>
                        <Button
                            className="cursor-pointer"
                            type="submit"
                            variant="destructive"
                            onClick={handleDelete}
                        >
                            Delete confirm
                        </Button>
                    </DialogFooter>
                </DialogPanel>
            </Dialog >

            {
                loading ? (
                    <div className="flex justify-center items-center" >
                        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
                    </div>
                ) : (
                    <div className="container flex flex-col items-center justify-center gap-12 px-4 py-4 scrollbar-white">
                        <div className="overflow-y-auto w-full">
                            <div className="flex flex-col space-y-2 justify-end items-end gap-4 mb-4">
                                <Textarea
                                    ref={textareaRef}
                                    placeholder="Enter description for selected posts (no word limit)"
                                    value={description}
                                    onChange={handleDescriptionChange}
                                    className="w-full resize-y"
                                    rows={4}
                                />
                                <Button
                                    onClick={handleSendSelectedPosts}
                                    disabled={isSubmitting || selectedPosts.length === 0}
                                    className="cursor-pointer bg-blue-600 hover:bg-blue-700 text-white"
                                >
                                    {isSubmitting ? 'Sending...' : 'Send Selected Posts'}
                                </Button>

                            </div>
                            <div>Total:{data?.length}</div>
                            <TKCommentsTable data={data} />

                        </div>
                    </div>
                )
            }

            <Pagination className="mt-6 cursor-pointer">
                <PaginationContent>
                    <PaginationItem>
                        <PaginationPrevious
                            onClick={() => handlePageChange(currentPage - 1)}
                            className={
                                currentPage === 1
                                    ? "pointer-events-none opacity-50"
                                    : "hover:bg-gray-100"
                            }
                        />
                    </PaginationItem>
                    {getPageNumbers().map((page) => (
                        <PaginationItem key={page}>
                            <PaginationLink
                                onClick={() => handlePageChange(page)}
                                isActive={page === currentPage}
                                className={
                                    page === currentPage
                                        ? "bg-blue-600 text-white hover:bg-blue-700"
                                        : "hover:bg-gray-100"
                                }
                            >
                                {page}
                            </PaginationLink>
                        </PaginationItem>
                    ))}
                    {totalPages > 5 && currentPage < totalPages - 2 && (
                        <PaginationItem>
                            <PaginationEllipsis />
                        </PaginationItem>
                    )}
                    <PaginationItem>
                        <PaginationNext
                            onClick={() => handlePageChange(currentPage + 1)}
                            className={
                                currentPage === totalPages
                                    ? "pointer-events-none opacity-50"
                                    : "hover:bg-gray-100"
                            }
                        />
                    </PaginationItem>
                </PaginationContent>
            </Pagination>

        </div >
    );
}