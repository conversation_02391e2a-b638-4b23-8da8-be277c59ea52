"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/animate-ui/components/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
    DollarSign,
    TrendingUp,
    Users,
    ShoppingCart,
    Target,
    BarChart3,
    Eye,
    MousePointer
} from "lucide-react";
import ListTkComments from "./listTKComments";
import ListTkProfile from "./listTKProfile";
import ListTkVideoWithHashTags from "./listTKVideoWithHashTags";
import URLAnalysisSection from "./URLAnalysisSection";

interface BusinessDashboardProps {
    userInfo: any;
    accounts: string[];
    onAccountsChange: (accounts: string[]) => void;
}

export default function BusinessDashboard({ userInfo, accounts, onAccountsChange }: BusinessDashboardProps) {
    const [urlInput, setUrlInput] = useState("");

    const businessMetrics = [
        {
            title: "总销售额",
            value: "¥125,680",
            change: "+18.2%",
            icon: DollarSign,
            color: "text-green-400"
        },
        {
            title: "转化率",
            value: "3.8%",
            change: "+0.5%",
            icon: Target,
            color: "text-blue-400"
        },
        {
            title: "商品曝光",
            value: "2.1M",
            change: "+25.3%",
            icon: Eye,
            color: "text-purple-400"
        },
        {
            title: "点击率",
            value: "4.2%",
            change: "+1.1%",
            icon: MousePointer,
            color: "text-orange-400"
        }
    ];

    const handleAnalyzeURL = () => {
        if (!urlInput.trim()) return;

        // TODO: 实现URL分析逻辑
        console.log("Analyzing URL for business:", urlInput);
    };

    return (
        <div className="space-y-6 w-full">
            {/* 电商主欢迎区域 */}
            <div className="bg-gradient-to-r from-green-600 to-blue-600 rounded-lg p-6 text-white">
                <h1 className="text-2xl font-bold mb-2">欢迎回来，{userInfo?.name || '电商主'}！</h1>
                <p className="text-green-100">分析您的商业表现，优化营销策略，提升ROI</p>
            </div>

            {/* 商业指标卡片 */}
            {/* <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {businessMetrics.map((metric, index) => {
                    const Icon = metric.icon;
                    return (
                        <Card key={index} className="bg-gray-900 border-gray-700">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-300">
                                    {metric.title}
                                </CardTitle>
                                <Icon className={`h-4 w-4 ${metric.color}`} />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-white">{metric.value}</div>
                                <p className="text-xs text-green-400">
                                    {metric.change} 相比上月
                                </p>
                            </CardContent>
                        </Card>
                    );
                })}
            </div> */}

            {/* URL分析区域 */}
            <URLAnalysisSection
                urlInput={urlInput}
                setUrlInput={setUrlInput}
                onAnalyze={handleAnalyzeURL}
                userType="business"
            />

            {/* 电商主专属功能标签页 */}
            {/* <Tabs defaultValue="sales" className="bg-[#262626] text-white rounded-lg">
                <TabsList className="grid w-full grid-cols-5 bg-[#262626]">
                    <TabsTrigger value="sales">销售分析</TabsTrigger>
                    <TabsTrigger value="competitor">竞品分析</TabsTrigger>
                    <TabsTrigger value="roi">ROI分析</TabsTrigger>
                    <TabsTrigger value="ads">广告投放</TabsTrigger>
                    <TabsTrigger value="insights">商业洞察</TabsTrigger>
                </TabsList>

                <TabsContents className="mx-1 mb-1 -mt-2 rounded-sm h-full bg-black">
                    <TabsContent value="sales" className="space-y-6 p-6">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <Card className="bg-gray-900 border-gray-700">
                                <CardHeader>
                                    <CardTitle className="text-white">销售趋势</CardTitle>
                                    <CardDescription className="text-gray-400">
                                        过去30天的销售表现
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        <div className="flex justify-between items-center">
                                            <span className="text-gray-300">本周销售</span>
                                            <span className="text-green-400 font-bold">¥28,560</span>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-gray-300">上周销售</span>
                                            <span className="text-white">¥24,120</span>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-gray-300">增长率</span>
                                            <span className="text-green-400">+18.4%</span>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card className="bg-gray-900 border-gray-700">
                                <CardHeader>
                                    <CardTitle className="text-white">热销商品</CardTitle>
                                    <CardDescription className="text-gray-400">
                                        本月销量排行
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        {[
                                            { name: '冬季保暖套装', sales: 156, revenue: '¥12,480' },
                                            { name: '护肤精华套盒', sales: 89, revenue: '¥8,900' },
                                            { name: '智能手表', sales: 67, revenue: '¥6,700' }
                                        ].map((product, index) => (
                                            <div key={index} className="flex justify-between items-center p-2 bg-gray-800 rounded">
                                                <div>
                                                    <p className="text-white text-sm">{product.name}</p>
                                                    <p className="text-gray-400 text-xs">销量: {product.sales}</p>
                                                </div>
                                                <span className="text-green-400 font-medium">{product.revenue}</span>
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>

                    <TabsContent value="competitor" className="space-y-6 p-6">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <Card className="bg-gray-900 border-gray-700">
                                <CardHeader>
                                    <CardTitle className="text-white">竞品对比</CardTitle>
                                    <CardDescription className="text-gray-400">
                                        与主要竞争对手的表现对比
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        {[
                                            { name: '您的品牌', followers: '125K', engagement: '4.2%', color: 'bg-blue-500' },
                                            { name: '竞品A', followers: '98K', engagement: '3.8%', color: 'bg-red-500' },
                                            { name: '竞品B', followers: '156K', engagement: '3.1%', color: 'bg-yellow-500' }
                                        ].map((brand, index) => (
                                            <div key={index} className="space-y-2">
                                                <div className="flex justify-between items-center">
                                                    <div className="flex items-center space-x-2">
                                                        <div className={`w-3 h-3 rounded-full ${brand.color}`}></div>
                                                        <span className="text-white text-sm">{brand.name}</span>
                                                    </div>
                                                    <span className="text-gray-300 text-sm">{brand.followers}</span>
                                                </div>
                                                <div className="flex justify-between items-center">
                                                    <span className="text-gray-400 text-xs">互动率</span>
                                                    <span className="text-green-400 text-xs">{brand.engagement}</span>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>

                            <Card className="bg-gray-900 border-gray-700">
                                <CardHeader>
                                    <CardTitle className="text-white">市场趋势</CardTitle>
                                    <CardDescription className="text-gray-400">
                                        行业热门话题和趋势
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        {[
                                            { trend: '可持续时尚', growth: '+45%', potential: '高' },
                                            { trend: '智能家居', growth: '+32%', potential: '中' },
                                            { trend: '健康生活', growth: '+28%', potential: '高' }
                                        ].map((item, index) => (
                                            <div key={index} className="flex justify-between items-center p-3 bg-gray-800 rounded-lg">
                                                <div>
                                                    <span className="text-white font-medium">{item.trend}</span>
                                                    <p className="text-gray-400 text-xs">增长: {item.growth}</p>
                                                </div>
                                                <span className={`px-2 py-1 rounded text-xs ${item.potential === '高' ? 'bg-green-600 text-white' : 'bg-yellow-600 text-white'
                                                    }`}>
                                                    {item.potential}潜力
                                                </span>
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>

                    <TabsContent value="roi" className="space-y-6 p-6">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <Card className="bg-gray-900 border-gray-700">
                                <CardHeader>
                                    <CardTitle className="text-white">投资回报率</CardTitle>
                                    <CardDescription className="text-gray-400">
                                        各渠道ROI表现
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        {[
                                            { channel: 'TikTok广告', spend: '¥5,000', revenue: '¥18,500', roi: '270%' },
                                            { channel: '达人合作', spend: '¥3,000', revenue: '¥12,000', roi: '300%' },
                                            { channel: '直播带货', spend: '¥2,000', revenue: '¥8,500', roi: '325%' }
                                        ].map((item, index) => (
                                            <div key={index} className="p-3 bg-gray-800 rounded-lg">
                                                <div className="flex justify-between items-center mb-2">
                                                    <span className="text-white font-medium">{item.channel}</span>
                                                    <span className="text-green-400 font-bold">{item.roi}</span>
                                                </div>
                                                <div className="flex justify-between text-sm">
                                                    <span className="text-gray-400">投入: {item.spend}</span>
                                                    <span className="text-gray-400">收入: {item.revenue}</span>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>

                            <Card className="bg-gray-900 border-gray-700">
                                <CardHeader>
                                    <CardTitle className="text-white">成本分析</CardTitle>
                                    <CardDescription className="text-gray-400">
                                        营销成本结构
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        {[
                                            { category: '广告投放', percentage: 45, amount: '¥4,500' },
                                            { category: '内容制作', percentage: 25, amount: '¥2,500' },
                                            { category: '达人合作', percentage: 20, amount: '¥2,000' },
                                            { category: '其他', percentage: 10, amount: '¥1,000' }
                                        ].map((item, index) => (
                                            <div key={index} className="flex justify-between items-center">
                                                <div className="flex items-center space-x-3">
                                                    <span className="text-gray-300">{item.category}</span>
                                                    <div className="w-16 bg-gray-700 rounded-full h-2">
                                                        <div
                                                            className="bg-blue-400 h-2 rounded-full"
                                                            style={{ width: `${item.percentage}%` }}
                                                        ></div>
                                                    </div>
                                                </div>
                                                <span className="text-white text-sm">{item.amount}</span>
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>

                    <TabsContent value="ads" className="space-y-6 p-6">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <Card className="bg-gray-900 border-gray-700">
                                <CardHeader>
                                    <CardTitle className="text-white">广告表现</CardTitle>
                                    <CardDescription className="text-gray-400">
                                        当前投放广告的表现数据
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        {[
                                            { name: '冬季新品推广', impressions: '125K', clicks: '5.2K', ctr: '4.16%', status: '进行中' },
                                            { name: '品牌宣传', impressions: '89K', clicks: '3.1K', ctr: '3.48%', status: '已暂停' }
                                        ].map((ad, index) => (
                                            <div key={index} className="p-3 bg-gray-800 rounded-lg">
                                                <div className="flex justify-between items-center mb-2">
                                                    <span className="text-white font-medium">{ad.name}</span>
                                                    <span className={`px-2 py-1 rounded text-xs ${ad.status === '进行中' ? 'bg-green-600 text-white' : 'bg-gray-600 text-white'
                                                        }`}>
                                                        {ad.status}
                                                    </span>
                                                </div>
                                                <div className="grid grid-cols-3 gap-2 text-sm">
                                                    <div>
                                                        <p className="text-gray-400">曝光</p>
                                                        <p className="text-white">{ad.impressions}</p>
                                                    </div>
                                                    <div>
                                                        <p className="text-gray-400">点击</p>
                                                        <p className="text-white">{ad.clicks}</p>
                                                    </div>
                                                    <div>
                                                        <p className="text-gray-400">CTR</p>
                                                        <p className="text-green-400">{ad.ctr}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>

                            <Card className="bg-gray-900 border-gray-700">
                                <CardHeader>
                                    <CardTitle className="text-white">投放建议</CardTitle>
                                    <CardDescription className="text-gray-400">
                                        AI智能投放优化建议
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        <div className="p-3 bg-blue-900/30 border border-blue-700 rounded-lg">
                                            <p className="text-blue-300 text-sm">💡 建议增加19-21点时段的投放预算</p>
                                        </div>
                                        <div className="p-3 bg-green-900/30 border border-green-700 rounded-lg">
                                            <p className="text-green-300 text-sm">🎯 女性25-35岁群体转化率最高</p>
                                        </div>
                                        <div className="p-3 bg-purple-900/30 border border-purple-700 rounded-lg">
                                            <p className="text-purple-300 text-sm">📈 视频广告比图片广告效果好35%</p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>

                    <TabsContent value="insights" className="space-y-6 p-6">
                        <Card className="bg-gray-900 border-gray-700">
                            <CardHeader>
                                <CardTitle className="text-white">商业洞察报告</CardTitle>
                                <CardDescription className="text-gray-400">
                                    基于数据分析的商业建议
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    <div className="p-4 bg-gradient-to-r from-blue-900/50 to-purple-900/50 border border-blue-700 rounded-lg">
                                        <h4 className="text-white font-semibold mb-2">📊 市场机会</h4>
                                        <p className="text-gray-300 text-sm">
                                            可持续时尚领域增长迅速，建议考虑推出环保产品线。预计可带来25%的新客户增长。
                                        </p>
                                    </div>
                                    <div className="p-4 bg-gradient-to-r from-green-900/50 to-blue-900/50 border border-green-700 rounded-lg">
                                        <h4 className="text-white font-semibold mb-2">🎯 用户画像优化</h4>
                                        <p className="text-gray-300 text-sm">
                                            核心用户群体为25-35岁职场女性，建议内容策略向职场、生活品质方向倾斜。
                                        </p>
                                    </div>
                                    <div className="p-4 bg-gradient-to-r from-purple-900/50 to-pink-900/50 border border-purple-700 rounded-lg">
                                        <h4 className="text-white font-semibold mb-2">💰 定价策略</h4>
                                        <p className="text-gray-300 text-sm">
                                            当前价格区间竞争激烈，建议通过增值服务提升客单价，而非单纯降价竞争。
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </TabsContents>
            </Tabs> */}
        </div>
    );
}
