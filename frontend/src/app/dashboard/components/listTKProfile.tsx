import { But<PERSON> } from "@/components/ui/button";
import {
    Table,
    TableBody,
    TableCaption,
    TableCell,
    TableFooter,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table"
import { tkAIAgentService, tkCommentsService, tkProfileService } from "@/utils/api";
import type { TKComment, TKCommentFromAPI, TKProfileFromAPI } from "@/utils/type";
import { useCallback, useEffect, useState } from "react";

import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination"
import { AbbreviatedText } from "@/utils/tools";
import { CopyButton } from "@/components/animate-ui/buttons/copy";
import { Loader2 } from "lucide-react";
import toast from "react-hot-toast";
import { Checkbox } from "@/components/ui/checkbox";


interface TKProfileResponse {
    data: TKProfileFromAPI[];
    total: number,
    page: number,
    page_size: number,
}

export default function ListTkProfile() {
    const [filter, setFilter] = useState("All");
    const [currentPage, setCurrentPage] = useState(1);
    const pageSize = 20;

    const [data, setData] = useState<TKProfileFromAPI[]>([]);
    const [totalPages, setTotalPages] = useState(1);
    const [loading, setLoading] = useState(false);
    const [selectedPosts, setSelectedPosts] = useState<string[]>([]); // Track selected post IDs
    const [isSubmitting, setIsSubmitting] = useState(false); // Track submission state

    const fetchComments = useCallback(async () => {
        setLoading(true);

        try {
            const params: Record<string, any> = {
                page: currentPage,
                page_size: pageSize,
            };

            if (filter !== "All") {
                params.status = filter;
            }

            const res = await tkProfileService.get<TKProfileResponse>(
                "",
                "application/json",
                { params }
            );

            setData(res.data);
            setTotalPages(Math.ceil(res.total / pageSize));
        } catch (error) {
            console.error("Failed to fetch comments:", error);
        } finally {
            setLoading(false);
        }
    }, [filter, currentPage]);

    useEffect(() => {
        fetchComments();
    }, [filter, currentPage]);


    const getPageNumbers = useCallback(() => {
        const pages = [];
        const maxVisiblePages = 5;
        const half = Math.floor(maxVisiblePages / 2);

        let start = Math.max(1, currentPage - half);
        let end = Math.min(totalPages, start + maxVisiblePages - 1);

        if (end - start + 1 < maxVisiblePages) {
            start = Math.max(1, end - maxVisiblePages + 1);
        }

        for (let i = start; i <= end; i++) {
            pages.push(i);
        }
        return pages;
    }, [currentPage, totalPages]);

    const handlePageChange = (page: number) => {
        if (page >= 1 && page <= totalPages) {
            setCurrentPage(page);
        }
    };

    const handleCheckboxChange = (postId: string) => {
        setSelectedPosts((prev) =>
            prev.includes(postId)
                ? prev.filter((id) => id !== postId)
                : [...prev, postId]
        );
    };

    // Handle sending selected posts to API
    const handleSendSelectedPosts = useCallback(async () => {
        if (selectedPosts.length === 0) {
            toast.error('Please select at least one post.');
            return;
        }

        setIsSubmitting(true);
        try {
            const response = await tkAIAgentService.post<{ message: string }>(
                '',
                { commentIds: [], profileVideoIDs: selectedPosts, videoHashTags: [] }, // Adjust payload as needed
            );
            toast.success('Selected posts sent successfully!');
            setSelectedPosts([]); // Clear selection after successful submission
        } catch (error) {
            console.error('Error sending selected posts:', error);
            toast.error('Failed to send selected posts.');
        } finally {
            setIsSubmitting(false);
        }
    }, [selectedPosts]);

    const TKCommentsTable = useCallback(({ data }: { data: TKProfileFromAPI[] }) => {
        return (
            <div>
                <Button
                    onClick={handleSendSelectedPosts}
                    disabled={isSubmitting || selectedPosts.length === 0}
                    className="mb-4 cursor-pointer bg-blue-600 hover:bg-blue-700 text-white"
                >
                    {isSubmitting ? 'Sending...' : 'Send Selected Posts'}
                </Button>
                <Table className="my-2">
                    <TableCaption>List of TikTok posts</TableCaption>
                    <TableHeader className="[&_th]:text-white bg-[#262626]">
                        <TableRow>
                            <TableHead className="w-[50px]">
                                <Checkbox
                                    id="select-all"
                                    checked={selectedPosts.length === data?.length && data?.length > 0}
                                    onCheckedChange={() =>
                                        setSelectedPosts(
                                            selectedPosts?.length === data?.length
                                                ? []
                                                : data.map((item) => item.video_id)
                                        )
                                    }
                                />
                            </TableHead>
                            <TableHead>User</TableHead>
                            <TableHead>Text</TableHead>
                            <TableHead>Likes</TableHead>
                            <TableHead>Video</TableHead>
                            <TableHead>Created At</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {data?.map((item) => (
                            <TableRow key={item.video_id}>
                                <TableCell>
                                    <Checkbox
                                        id={`toggle-${item.video_id}`}
                                        checked={selectedPosts.includes(item.video_id)}
                                        onCheckedChange={() => handleCheckboxChange(item.video_id)}
                                    />
                                </TableCell>
                                <TableCell className="flex items-center gap-2">
                                    <img
                                        src={item.author_avatar_url}
                                        alt={item.author_nickname}
                                        className="w-8 h-8 rounded-full"
                                    />
                                    <span>{item.author_nickname}</span>
                                </TableCell>
                                <TableCell>
                                    {AbbreviatedText({ text: item.video_text, maxLength: 80 })}
                                    <CopyButton content={item.video_text} size="sm" />
                                </TableCell>
                                <TableCell>{item.digg_count}</TableCell>
                                <TableCell>
                                    <a
                                        href={item.video_url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-blue-600 underline"
                                    >
                                        Link
                                    </a>
                                </TableCell>
                                <TableCell>{new Date(item.create_time).toLocaleString()}</TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>
        );
    }, [data, selectedPosts]);



    return (
        <div className="flex flex-col items-center justify-center h-full p-2">
            <h1 className="text-2xl font-bold mb-4">TikTok Comments</h1>

            {loading ? (
                <div className="flex justify-center items-center">
                    <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
                </div>
            ) : (
                <div className="container flex flex-col items-center justify-center gap-12 px-4 py-4 scrollbar-white">
                    <div className="overflow-y-auto w-full">
                        <div>Total:{data?.length}</div>
                        <TKCommentsTable data={data} />

                    </div>
                </div>
            )}

            <Pagination className="mt-6 cursor-pointer">
                <PaginationContent>
                    <PaginationItem>
                        <PaginationPrevious
                            onClick={() => handlePageChange(currentPage - 1)}
                            className={
                                currentPage === 1
                                    ? "pointer-events-none opacity-50"
                                    : "hover:bg-gray-100"
                            }
                        />
                    </PaginationItem>
                    {getPageNumbers().map((page) => (
                        <PaginationItem key={page}>
                            <PaginationLink
                                onClick={() => handlePageChange(page)}
                                isActive={page === currentPage}
                                className={
                                    page === currentPage
                                        ? "bg-blue-600 text-white hover:bg-blue-700"
                                        : "hover:bg-gray-100"
                                }
                            >
                                {page}
                            </PaginationLink>
                        </PaginationItem>
                    ))}
                    {totalPages > 5 && currentPage < totalPages - 2 && (
                        <PaginationItem>
                            <PaginationEllipsis />
                        </PaginationItem>
                    )}
                    <PaginationItem>
                        <PaginationNext
                            onClick={() => handlePageChange(currentPage + 1)}
                            className={
                                currentPage === totalPages
                                    ? "pointer-events-none opacity-50"
                                    : "hover:bg-gray-100"
                            }
                        />
                    </PaginationItem>
                </PaginationContent>
            </Pagination>

        </div>
    );
}