import { StarsBackground } from "@/components/animate-ui/backgrounds/stars";
import Header from "@/components/Header";
import ClientHeaderWrapper from "@/context/ClientHeaderWrapper";
import "@/styles/globals.css";

import type { Metadata } from "next";
import { Exo_2 } from "next/font/google";
import { Toaster } from 'react-hot-toast';

export const metadata: Metadata = {
	title: "AI TK",
	description: "AI Agent TIKTOK",
	icons: [{ rel: "icon", url: "/favicon.ico" }],
};


const exo2 = Exo_2({
	subsets: ["latin"],
	weight: ["400", "500", "600", "700"],
	variable: "--font-exo-2",
});

export default function RootLayout({
	children,
}: Readonly<{ children: React.ReactNode }>) {
	return (
		<html lang="en" className={`${exo2.className}`}>
			<body>
				<ClientHeaderWrapper />
				<StarsBackground className="fixed inset-0 flex items-center justify-center rounded-xl z-[-10]" />
				<Toaster />
				<main className="flex min-h-screen flex-col items-center justify-center  ">
					<div className="container flex flex-col items-center justify-center  ">
						{children}
					</div>
				</main>
			</body>
		</html>
	);
}
