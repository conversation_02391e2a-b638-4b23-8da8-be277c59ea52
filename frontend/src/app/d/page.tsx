"use client";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/animate-ui/components/tabs";
import { useTikTokUser } from "@/hooks/useGithubUser";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import {
    Dialog,
    DialogBackdrop,
    DialogPanel,
    DialogTitle,
    DialogDescription,
    DialogHeader,
    DialogFooter,
} from '@/components/animate-ui/headless/dialog';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { tkAuthorIDsService } from "@/utils/api";
import toast from "react-hot-toast";
import D1 from "./components/d1";


export default function DashboardPage() {
    const { loading, error, userInfo } = useTikTokUser();
    const router = useRouter();
    const [accounts, setAccounts] = useState<string[]>([]);
    const [input, setInput] = useState("");
    const [toDelete, setToDelete] = useState<string | null>(null);
    const [isOpen, setIsOpen] = useState(false);



    useEffect(() => {
        if (error) {

            router.push("/login");
        }
    }, [error, router]);

    useEffect(() => {
        if (userInfo?.author_unique_ids) {
            setAccounts(userInfo.author_unique_ids);
        }
    }, [userInfo]);

    if (loading) {
        return <div className="text-white">Loading...</div>;
    }

    const handleAdd = async () => {
        const val = input.trim();
        if (!val) {
            toast.error("Empty input!");
            return
        }
        if (accounts.includes(val)) {
            toast.error("Already has!");
            return
        }
        const newAccounts = [...accounts, val];
        const x = await tkAuthorIDsService.post('', { authorIds: newAccounts })
        console.log(x);
        toast.success("Successful")
        setAccounts(newAccounts);
        setInput("");
    };

    const handleDelete = async () => {
        if (toDelete) {
            const newAccounts = accounts.filter(acc => acc !== toDelete);
            setAccounts(newAccounts);
            await tkAuthorIDsService.post('', { authorIds: newAccounts });
            setToDelete(null);
            setIsOpen(false);
            toast.success("Successfully deleted!");
        }
    };


    return (

        <div className="my-32 w-full ">

            {/* Modal Confirm */}
            <Dialog open={isOpen} onClose={() => setIsOpen(false)}>
                <DialogBackdrop />

                <DialogPanel className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>Confirm deletion</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to delete account <b>{toDelete}</b> ?
                        </DialogDescription>
                    </DialogHeader>

                    <div className="grid gap-4 py-4">
                        <p>
                            Delete confirm
                        </p>
                    </div>

                    <DialogFooter className="cursor-pointer">
                        <Button className="cursor-pointer" variant="outline" onClick={() => setIsOpen(false)}>
                            Cancel
                        </Button>
                        <Button
                            className="cursor-pointer"
                            type="submit"
                            variant="destructive"
                            onClick={handleDelete}
                        >
                            Delete confirm
                        </Button>
                    </DialogFooter>
                </DialogPanel>
            </Dialog>
            <Tabs defaultValue="comments" className="custom-a  bg-[#262626] text-white rounded-lg">
                <TabsList className="grid w-full grid-cols-2 bg-[#262626]">
                    <TabsTrigger value="comments">Comments</TabsTrigger>
                    <TabsTrigger value="profile">Profile</TabsTrigger>
                </TabsList>

                <TabsContents className="mx-1 mb-1 -mt-2 rounded-sm h-full bg-black">
                    <TabsContent value="comments" className="space-y-6 p-6">
                        <D1 />
                    </TabsContent>
                    <TabsContent value="profile" className="space-y-6 p-6">
                        1
                    </TabsContent>
                </TabsContents>
            </Tabs>
        </div>
    )

}