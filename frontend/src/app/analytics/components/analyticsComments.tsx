import { Button } from "@/components/ui/button";
import {
    Table,
    TableBody,
    TableCaption,
    TableCell,
    TableFooter,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table"
import { AbbreviatedText } from "@/utils/tools";
import type { AnalyticsComments } from "@/utils/type";

export const TKAnalyticsCommentsTable = ({ data }: { data: AnalyticsComments[] }) => {

    if (data && data.length === 0) {
        return <div className="text-white h-full flex flex-col items-center justify-center">
            <div>No Data</div>
            <img className="w-32 h-32" src="/nodata.png" alt="" />
        </div>
    }

    return (
        <Table className="my-2 overflow-auto">
            <TableCaption>List of TikTok comments</TableCaption>
            <TableHeader className="[&_th]:text-white bg-[#262626]">
                <TableRow>
                    <TableHead>Video</TableHead>
                    <TableHead>Text</TableHead>
                    <TableHead>Intent Score</TableHead>
                    <TableHead>Labels</TableHead>
                    <TableHead>Urls</TableHead>
                    <TableHead>Final Score</TableHead>
                    <TableHead>Scenario</TableHead>
                </TableRow>
            </TableHeader>
            <TableBody>
                {data?.map((item) => (
                    <TableRow key={item.id}>
                        <TableCell>
                            <a
                                href={item.video_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 underline"
                            >
                                Link
                            </a>
                        </TableCell>
                        <TableCell>{AbbreviatedText({ text: item.text, maxLength: 80 })}</TableCell>
                        <TableCell>{item.intent_score}</TableCell>
                        <TableCell>
                            {item.labels?.map((label) => (
                                <span key={label} className="inline-block bg-yellow-400 text-black px-2 py-0.5 mr-1 rounded">
                                    {label}
                                </span>
                            ))}
                        </TableCell>
                        <TableCell>
                            {item.urls?.map((url) => (
                                <a
                                    key={url}
                                    href={url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-blue-600 underline mr-1"
                                >
                                    Link
                                </a>
                            ))}
                        </TableCell>
                        <TableCell>{item.final_score}</TableCell>
                        <TableCell>{item.scenario_reinforcement}</TableCell>
                    </TableRow>
                ))}
            </TableBody>
        </Table>

    );
}