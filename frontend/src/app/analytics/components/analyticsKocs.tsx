import {
    Table,
    TableBody,
    TableCaption,
    TableCell,
    Table<PERSON>ooter,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table"
import { AbbreviatedText } from "@/utils/tools";
import type { AnalyticsKocs } from "@/utils/type";

export const TKAnalyticsKocsTable = ({ data }: { data: AnalyticsKocs[] }) => {

    if (!data || data?.length === 0) {
        return <div className="text-white h-full flex flex-col items-center justify-center">
            <div>No Data</div>
            <img className="w-32 h-32" src="/nodata.png" alt="" />
        </div>
    }

    return <Table className="my-2">
        <TableCaption>List of AI Sorted KOCs</TableCaption>
        <TableHeader className="[&_th]:text-white bg-[#262626]">
            <TableRow>
                <TableHead>Video</TableHead>
                <TableHead>Intent Score</TableHead>
                <TableHead>Labels</TableHead>
                <TableHead>Urls</TableHead>
                <TableHead>Final Score</TableHead>
                <TableHead>Scenario</TableHead>
            </TableRow>
        </TableHeader>
        <TableBody>
            {data?.map((item) => (
                <TableRow key={item.id}>
                    <TableCell>
                        <a
                            href={item.video_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 underline"
                        >
                            Link
                        </a>
                    </TableCell>
                    <TableCell>{item.intent_score}</TableCell>
                    <TableCell>
                        {item.labels?.map((label, idx) => (
                            <span key={`label-${idx}`} className="inline-block bg-yellow-400 text-black px-2 py-0.5 mr-1 rounded">
                                {label}
                            </span>
                        ))}
                    </TableCell>
                    <TableCell>
                        {item.urls?.map((url, idx) => (
                            <a
                                key={`url-${idx}`}
                                href={url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 underline mr-1"
                            >
                                Link
                            </a>
                        ))}
                    </TableCell>
                    <TableCell>{item.final_score}</TableCell>
                    <TableCell>{item.scenario_reinforcement}</TableCell>
                </TableRow>
            ))}
        </TableBody>
    </Table>
}