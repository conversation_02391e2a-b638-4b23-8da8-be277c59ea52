"use client";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/animate-ui/components/tabs";
import { useTikTokUser } from "@/hooks/useGithubUser";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import {
    Dialog,
    DialogBackdrop,
    DialogPanel,
    DialogTitle,
    DialogDescription,
    DialogHeader,
    DialogFooter,
} from '@/components/animate-ui/headless/dialog';
import { Button } from "@/components/ui/button";
import { tkAnalyticsService, tkAuthorIDsService } from "@/utils/api";
import toast from "react-hot-toast";
import type { AnalyticsComments, AnalyticsKocs } from "@/utils/type";
import { AbbreviatedText } from "@/utils/tools";
import { TKAnalyticsCommentsTable } from "./components/analyticsComments";
import { TKAnalyticsKocsTable } from "./components/analyticsKocs";

export default function DashboardPage() {
    const { loading, error, userInfo } = useTikTokUser();
    const router = useRouter();

    const [accounts, setAccounts] = useState<string[]>([]);
    const [analyticsComments, setAnalyticsComments] = useState<AnalyticsComments[]>([]);
    const [analyticsKocs, setAnalyticsKocs] = useState<AnalyticsKocs[]>([]);

    const [isOpen, setIsOpen] = useState(false);



    useEffect(() => {
        if (error) {
            router.push("/login");
        }
        handleAdd()
    }, [error, router]);

    useEffect(() => {
        if (userInfo?.authorUniqueIds) {
            setAccounts(userInfo.authorUniqueIds);
        }
    }, [userInfo]);

    if (loading) {
        return <div className="text-white">Loading...</div>;
    }

    const handleAdd = async () => {
        const res = await tkAnalyticsService.get<{ data: { comments: AnalyticsComments[], kocs: AnalyticsKocs[] } }>('')
        console.log(res);
        setAnalyticsComments(res.data.comments)
        setAnalyticsKocs(res.data.kocs)
        toast.success("Successful")

    };

    const handleDelete = async () => {
        setIsOpen(false);
        toast.success("Successfully deleted!");
    };

    return (
        <div className="my-32 w-full ">
            {/* Modal Confirm */}
            <Dialog open={isOpen} onClose={() => setIsOpen(false)}>
                <DialogBackdrop />

                <DialogPanel className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>Confirm deletion</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to delete account <b>{ }</b> ?
                        </DialogDescription>
                    </DialogHeader>

                    <div className="grid gap-4 py-4">
                        <p>
                            Delete confirm
                        </p>
                    </div>

                    <DialogFooter className="cursor-pointer">
                        <Button className="cursor-pointer" variant="outline" onClick={() => setIsOpen(false)}>
                            Cancel
                        </Button>
                        <Button
                            className="cursor-pointer"
                            type="submit"
                            variant="destructive"
                            onClick={handleDelete}
                        >
                            Delete confirm
                        </Button>
                    </DialogFooter>
                </DialogPanel>
            </Dialog>


            <Tabs defaultValue="sortedComments" className="custom-a  bg-[#262626] text-white rounded-lg">
                <TabsList className="grid w-full grid-cols-2 bg-[#262626]">
                    <TabsTrigger value="sortedComments">sortedComments</TabsTrigger>
                    <TabsTrigger value="sortedKocs">sortedKocs</TabsTrigger>
                </TabsList>

                <TabsContents className="mx-1 mb-1 -mt-2 rounded-sm h-full bg-black">
                    <TabsContent value="sortedComments" className="space-y-6 p-6">
                        {analyticsComments?.length > 0 && <TKAnalyticsCommentsTable data={analyticsComments} />}
                    </TabsContent>
                    <TabsContent value="sortedKocs" className="space-y-6 p-6">
                        {analyticsKocs?.length > 0 && <TKAnalyticsKocsTable data={analyticsKocs} />}

                    </TabsContent>

                </TabsContents>
            </Tabs>
        </div>
    )

}