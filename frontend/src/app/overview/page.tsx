"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { useTikTokUser } from "@/hooks/useGithubUser";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, TabsContents, TabsList, TabsTrigger } from "@/components/animate-ui/components/tabs";
import { tkCommentsService, tkProfileService, tkVideoWithHashTagsService, tkAnalyticsService } from "@/utils/api";
import { TKCommentFromAPI, TKProfileFromAPI, TKVideoWithHashTagsFromAPI, AnalyticsComments, AnalyticsKocs } from "@/utils/type";
import { Loader2, Users, MessageCircle, Video, TrendingUp, Heart, Share, Eye, Hash } from "lucide-react";
import toast from "react-hot-toast";

interface DashboardStats {
  totalComments: number;
  totalProfiles: number;
  totalVideos: number;
  totalLikes: number;
  totalShares: number;
  totalViews: number;
  avgEngagementRate: number;
  topHashtags: Array<{ name: string; count: number }>;
}

interface RecentActivity {
  comments: TKCommentFromAPI[];
  profiles: TKProfileFromAPI[];
  videos: TKVideoWithHashTagsFromAPI[];
}

export default function OverviewDashboard() {
  const { loading, error, userInfo } = useTikTokUser();
  const router = useRouter();
  
  const [stats, setStats] = useState<DashboardStats>({
    totalComments: 0,
    totalProfiles: 0,
    totalVideos: 0,
    totalLikes: 0,
    totalShares: 0,
    totalViews: 0,
    avgEngagementRate: 0,
    topHashtags: []
  });
  
  const [recentActivity, setRecentActivity] = useState<RecentActivity>({
    comments: [],
    profiles: [],
    videos: []
  });

  const [analyticsData, setAnalyticsData] = useState<{
    comments: AnalyticsComments[];
    kocs: AnalyticsKocs[];
  }>({
    comments: [],
    kocs: []
  });

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (error) {
      router.push("/login");
    }
  }, [error, router]);

  const fetchDashboardData = useCallback(async () => {
    if (!userInfo) return;
    
    setIsLoading(true);
    try {
      // Fetch comments
      const commentsRes = await tkCommentsService.get<{
        data: TKCommentFromAPI[];
        total: number;
      }>("", "application/json", {
        params: { page: 1, page_size: 5 }
      });

      // Fetch profiles
      const profilesRes = await tkProfileService.get<{
        data: TKProfileFromAPI[];
        total: number;
      }>("", "application/json", {
        params: { page: 1, page_size: 5 }
      });

      // Fetch videos with hashtags
      const videosRes = await tkVideoWithHashTagsService.get<{
        data: TKVideoWithHashTagsFromAPI[];
        total: number;
      }>("", "application/json", {
        params: { page: 1, page_size: 5 }
      });

      // Fetch analytics data
      const analyticsRes = await tkAnalyticsService.get<{
        data: { comments: AnalyticsComments[]; kocs: AnalyticsKocs[] };
      }>("");

      // Calculate stats
      const totalLikes = profilesRes.data.reduce((sum, profile) => sum + profile.digg_count, 0);
      const totalShares = profilesRes.data.reduce((sum, profile) => sum + profile.share_count, 0);
      const totalViews = profilesRes.data.reduce((sum, profile) => sum + profile.play_count, 0);
      const totalComments = profilesRes.data.reduce((sum, profile) => sum + profile.comment_count, 0);

      // Calculate engagement rate
      const avgEngagementRate = totalViews > 0 ? 
        ((totalLikes + totalShares + totalComments) / totalViews) * 100 : 0;

      // Extract hashtags (simplified - you might want to improve this)
      const hashtags: { [key: string]: number } = {};
      videosRes.data.forEach(video => {
        if (video.search_hashtag) {
          hashtags[video.search_hashtag] = (hashtags[video.search_hashtag] || 0) + 1;
        }
      });

      const topHashtags = Object.entries(hashtags)
        .map(([name, count]) => ({ name, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);

      setStats({
        totalComments: commentsRes.total,
        totalProfiles: profilesRes.total,
        totalVideos: videosRes.total,
        totalLikes,
        totalShares,
        totalViews,
        avgEngagementRate,
        topHashtags
      });

      setRecentActivity({
        comments: commentsRes.data,
        profiles: profilesRes.data,
        videos: videosRes.data
      });

      setAnalyticsData(analyticsRes.data);

    } catch (error) {
      console.error("Failed to fetch dashboard data:", error);
      toast.error("Failed to load dashboard data");
    } finally {
      setIsLoading(false);
    }
  }, [userInfo]);

  useEffect(() => {
    if (userInfo) {
      fetchDashboardData();
    }
  }, [userInfo, fetchDashboardData]);

  if (loading || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
      </div>
    );
  }

  return (
    <div className="my-8 w-full max-w-7xl mx-auto px-4">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Dashboard Overview</h1>
        <p className="text-gray-400">Welcome back, {userInfo?.name}! Here's your TikTok analytics overview.</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card className="bg-[#262626] border-gray-700">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-300">Total Comments</CardTitle>
            <MessageCircle className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{stats.totalComments.toLocaleString()}</div>
          </CardContent>
        </Card>

        <Card className="bg-[#262626] border-gray-700">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-300">Total Profiles</CardTitle>
            <Users className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{stats.totalProfiles.toLocaleString()}</div>
          </CardContent>
        </Card>

        <Card className="bg-[#262626] border-gray-700">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-300">Total Videos</CardTitle>
            <Video className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{stats.totalVideos.toLocaleString()}</div>
          </CardContent>
        </Card>

        <Card className="bg-[#262626] border-gray-700">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-300">Engagement Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{stats.avgEngagementRate.toFixed(2)}%</div>
          </CardContent>
        </Card>
      </div>

      {/* Engagement Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="bg-[#262626] border-gray-700">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-300">Total Likes</CardTitle>
            <Heart className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{stats.totalLikes.toLocaleString()}</div>
          </CardContent>
        </Card>

        <Card className="bg-[#262626] border-gray-700">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-300">Total Shares</CardTitle>
            <Share className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{stats.totalShares.toLocaleString()}</div>
          </CardContent>
        </Card>

        <Card className="bg-[#262626] border-gray-700">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-300">Total Views</CardTitle>
            <Eye className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{stats.totalViews.toLocaleString()}</div>
          </CardContent>
        </Card>
      </div>

      {/* Top Hashtags */}
      {stats.topHashtags.length > 0 && (
        <Card className="bg-[#262626] border-gray-700 mb-8">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Hash className="h-5 w-5" />
              Top Hashtags
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {stats.topHashtags.map((hashtag, index) => (
                <div key={hashtag.name} className="flex justify-between items-center">
                  <span className="text-gray-300">#{hashtag.name}</span>
                  <span className="text-white font-medium">{hashtag.count}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Activity */}
      <Card className="bg-[#262626] border-gray-700 mb-8">
        <CardHeader>
          <CardTitle className="text-white">Recent Activity</CardTitle>
          <CardDescription className="text-gray-400">Latest data from your TikTok monitoring</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="comments" className="w-full">
            <TabsList className="grid w-full grid-cols-3 bg-[#1a1a1a]">
              <TabsTrigger value="comments" className="text-white">Recent Comments</TabsTrigger>
              <TabsTrigger value="profiles" className="text-white">Recent Profiles</TabsTrigger>
              <TabsTrigger value="videos" className="text-white">Recent Videos</TabsTrigger>
            </TabsList>
            
            <TabsContents className="mt-4">
              <TabsContent value="comments" className="space-y-4">
                {recentActivity.comments.length > 0 ? (
                  recentActivity.comments.map((comment) => (
                    <div key={comment.id} className="border-l-4 border-blue-600 pl-4 py-2">
                      <div className="flex items-center gap-2 mb-1">
                        <img 
                          src={comment.avatar_url} 
                          alt={comment.author_unique_id}
                          className="w-6 h-6 rounded-full"
                        />
                        <span className="text-white font-medium">{comment.author_unique_id}</span>
                        <span className="text-gray-400 text-sm">
                          {new Date(comment.created_at).toLocaleDateString()}
                        </span>
                      </div>
                      <p className="text-gray-300 text-sm">{comment.comment_text}</p>
                      <div className="flex items-center gap-4 mt-2">
                        <span className="text-red-600 text-sm flex items-center gap-1">
                          <Heart className="h-3 w-3" />
                          {comment.digg_count}
                        </span>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-400">No recent comments available</p>
                )}
              </TabsContent>
              
              <TabsContent value="profiles" className="space-y-4">
                {recentActivity.profiles.length > 0 ? (
                  recentActivity.profiles.map((profile) => (
                    <div key={profile.video_id} className="border-l-4 border-green-600 pl-4 py-2">
                      <div className="flex items-center gap-2 mb-1">
                        <img 
                          src={profile.author_avatar_url} 
                          alt={profile.author_nickname}
                          className="w-6 h-6 rounded-full"
                        />
                        <span className="text-white font-medium">{profile.author_nickname}</span>
                        <span className="text-gray-400 text-sm">
                          {new Date(profile.created_at).toLocaleDateString()}
                        </span>
                      </div>
                      <p className="text-gray-300 text-sm">{profile.video_text}</p>
                      <div className="flex items-center gap-4 mt-2">
                        <span className="text-red-600 text-sm flex items-center gap-1">
                          <Heart className="h-3 w-3" />
                          {profile.digg_count}
                        </span>
                        <span className="text-blue-600 text-sm flex items-center gap-1">
                          <Share className="h-3 w-3" />
                          {profile.share_count}
                        </span>
                        <span className="text-yellow-600 text-sm flex items-center gap-1">
                          <Eye className="h-3 w-3" />
                          {profile.play_count}
                        </span>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-400">No recent profiles available</p>
                )}
              </TabsContent>
              
              <TabsContent value="videos" className="space-y-4">
                {recentActivity.videos.length > 0 ? (
                  recentActivity.videos.map((video) => (
                    <div key={video.id} className="border-l-4 border-purple-600 pl-4 py-2">
                      <div className="flex items-center gap-2 mb-1">
                        <img 
                          src={video.avatar_url} 
                          alt={video.nickname}
                          className="w-6 h-6 rounded-full"
                        />
                        <span className="text-white font-medium">{video.nickname}</span>
                        <span className="text-gray-400 text-sm">
                          {new Date(video.created_at).toLocaleDateString()}
                        </span>
                      </div>
                      <p className="text-gray-300 text-sm">{video.text}</p>
                      {video.search_hashtag && (
                        <span className="text-blue-600 text-sm">#{video.search_hashtag}</span>
                      )}
                      <div className="flex items-center gap-4 mt-2">
                        <span className="text-red-600 text-sm flex items-center gap-1">
                          <Heart className="h-3 w-3" />
                          {video.digg_count}
                        </span>
                        <span className="text-blue-600 text-sm flex items-center gap-1">
                          <Share className="h-3 w-3" />
                          {video.share_count}
                        </span>
                        <span className="text-yellow-600 text-sm flex items-center gap-1">
                          <Eye className="h-3 w-3" />
                          {video.play_count}
                        </span>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-400">No recent videos available</p>
                )}
              </TabsContent>
            </TabsContents>
          </Tabs>
        </CardContent>
      </Card>

      {/* Analytics Summary */}
      {(analyticsData.comments.length > 0 || analyticsData.kocs.length > 0) && (
        <Card className="bg-[#262626] border-gray-700">
          <CardHeader>
            <CardTitle className="text-white">Analytics Summary</CardTitle>
            <CardDescription className="text-gray-400">AI-powered insights from your TikTok data</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">Comment Analytics</h3>
                <p className="text-3xl font-bold text-blue-600">{analyticsData.comments.length}</p>
                <p className="text-gray-400 text-sm">Processed comments</p>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">KOC Analytics</h3>
                <p className="text-3xl font-bold text-green-600">{analyticsData.kocs.length}</p>
                <p className="text-gray-400 text-sm">Processed KOCs</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card className="bg-[#262626] border-gray-700 mt-8">
        <CardHeader>
          <CardTitle className="text-white">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button 
              onClick={() => router.push('/fetch')}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Fetch New Data
            </Button>
            <Button 
              onClick={() => router.push('/dashboard')}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              View Dashboard
            </Button>
            <Button 
              onClick={() => router.push('/analytics')}
              className="bg-purple-600 hover:bg-purple-700 text-white"
            >
              View Analytics
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
