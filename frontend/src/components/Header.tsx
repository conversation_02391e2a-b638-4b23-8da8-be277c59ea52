"use client";

import Link from "next/link";
import { useTikTokUser } from "@/hooks/useGithubUser";
import { RollingText } from "./animate-ui/text/rolling";

export default function Header() {
    const { userInfo, loading, error } = useTikTokUser();

    if (error) return <div>Error</div>

    return (
        <header className="w-full border-b border-gray-200 bg-white fixed z-10">
            <div className="mx-auto flex max-w-7xl items-center justify-between px-4 py-4">
                <div className="flex items-center space-x-4">

                    <div className="text-xl font-bold text-gray-800">
                        <Link href="/">
                            <RollingText inViewOnce={false} inView={true} className="text-4xl" text="AITK" />
                        </Link>

                    </div>

                    <nav className="space-x-6 hidden md:block">
                        <Link href="/" className="text-gray-600 hover:text-gray-900">
                            Home
                        </Link>
                        <Link href="/dashboard" className="text-gray-600 hover:text-gray-900">
                            Dashboard
                        </Link>
                        <Link href="/fetch" className="text-gray-600 hover:text-gray-900">
                            Fetch
                        </Link>
                        <Link href="/analytics" className="text-gray-600 hover:text-gray-900">
                            Analytics
                        </Link>
                        <Link href="/d" className="text-gray-600 hover:text-gray-900">
                            DDD
                        </Link>
                    </nav>
                </div>

                <div className="flex items-center space-x-4">
                    {loading ? (
                        <span className="text-sm text-gray-600">Loading...</span>
                    ) : !error && userInfo ? (
                        <div className="flex items-center space-x-2">
                            <div>
                                {userInfo?.name}
                            </div>

                            <img
                                src={userInfo?.avatar_url}
                                alt="avatar"
                                className="h-8 w-8 rounded-full"
                            />
                        </div>

                    ) : (
                        <Link
                            href="/login"
                            className="rounded-md bg-emerald-500 px-4 py-2 text-white hover:bg-emerald-600"
                        >
                            Login
                        </Link>
                    )}
                </div>
            </div>
        </header>
    );
}
