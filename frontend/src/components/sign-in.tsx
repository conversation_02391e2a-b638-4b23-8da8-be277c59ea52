"use client";

import { useEffect, useState } from "react";
import { Button } from "./ui/button";
import { UserType } from "@/utils/type";

interface SignInProps {
    userType?: UserType | null;
}

export default function SignIn({ userType }: SignInProps) {
    const [error, setError] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(false);

    const signIn = async () => {
        if (!userType) {
            setError("请先选择用户类型");
            return;
        }

        setIsLoading(true);
        setError(null);

        try {
            const url = new URL(process.env.NEXT_PUBLIC_API_BASE_URL + "/oauth/tiktok");
            url.searchParams.append("user_type", userType);

            const response = await fetch(url.toString(), {
                method: "GET",
                headers: { "Content-Type": "application/json" },
                credentials: "include",
            });

            const data = await response.json();
            if (data.url) {
                window.location.href = data.url;
            } else {
                setError("TikTok OAuth URL not found.");
            }
        } catch (err) {
            setError("Failed to initiate TikTok login");
            console.error(err);
        } finally {
            setIsLoading(false);
        }
    };


    return (
        <div className="flex flex-col items-center justify-center">
            <div className="p-8 rounded-lg shadow-lg max-w-md w-full">
                {userType && (
                    <div className="mb-4 text-center">
                        <p className="text-gray-300 text-sm">
                            登录类型: <span className="text-blue-400 font-semibold">
                                {userType === 'personal' ? '个人用户' : '电商主'}
                            </span>
                        </p>
                    </div>
                )}

                <form
                    className="cursor-pointer text-center"
                    onSubmit={(e) => {
                        e.preventDefault();
                        signIn();
                    }}
                >
                    <Button
                        type="submit"
                        disabled={isLoading || !userType}
                        className="bg-white cursor-pointer text-black hover:bg-[#d1d1d1] disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        {isLoading ? '登录中...' : 'Sign in with TikTok'}
                    </Button>
                </form>

                {error && <p className="text-red-500 mt-4 text-center">{error}</p>}
            </div>
        </div>
    );
}
