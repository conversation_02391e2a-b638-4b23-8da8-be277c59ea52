"use client";

import { useState } from "react";
import { But<PERSON> } from "./ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card";
import type { UserType } from "@/utils/type";
import { CheckCircle, User, Building2 } from "lucide-react";

interface UserTypeSelectorProps {
    onSelect: (userType: UserType) => void;
    isLoading?: boolean;
}

export default function UserTypeSelector({ onSelect, isLoading = false }: UserTypeSelectorProps) {
    const [selectedType, setSelectedType] = useState<UserType | null>(null);

    const userTypes = [
        {
            type: 'personal' as UserType,
            title: '个人TikTok用户',
            description: '分析个人内容表现，获取粉丝洞察和内容优化建议',
            icon: User,
            features: [
                '个人内容分析',
                '粉丝增长趋势',
                '评论情感分析',
                'AI内容建议',
                '基础数据统计'
            ],
            color: 'border-blue-200 hover:border-blue-400'
        },
        {
            type: 'business' as UserType,
            title: '电商主/企业用户',
            description: '深度商业分析，竞品研究，营销效果评估和ROI分析',
            icon: Building2,
            features: [
                '商业数据分析',
                '竞品分析',
                'ROI分析',
                '营销效果评估',
                '广告投放建议',
                '高级数据导出',
                '商业API访问'
            ],
            color: 'border-purple-200 hover:border-purple-400'
        }
    ];

    const handleSelect = (type: UserType) => {
        setSelectedType(type);
    };

    const handleConfirm = () => {
        if (selectedType) {
            onSelect(selectedType);
        }
    };

    return (
        <div className="min-h-screen bg-black flex items-center justify-center p-4">
            <div className="max-w-4xl w-full">
                <div className="text-center mb-8">
                    <h1 className="text-3xl font-bold text-white mb-2">选择您的用户类型</h1>
                    <p className="text-gray-400">根据您的需求选择合适的用户类型，享受定制化的分析体验</p>
                </div>

                <div className="grid md:grid-cols-2 gap-6 mb-8">
                    {userTypes.map((userType) => {
                        const Icon = userType.icon;
                        const isSelected = selectedType === userType.type;

                        return (
                            <Card
                                key={userType.type}
                                className={`cursor-pointer transition-all duration-200 bg-gray-900 text-white border-2 ${isSelected
                                        ? 'border-green-400 ring-2 ring-green-400/20'
                                        : userType.color
                                    }`}
                                onClick={() => handleSelect(userType.type)}
                            >
                                <CardHeader className="pb-4">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                            <Icon className="h-8 w-8 text-blue-400" />
                                            <div>
                                                <CardTitle className="text-xl">{userType.title}</CardTitle>
                                            </div>
                                        </div>
                                        {isSelected && (
                                            <CheckCircle className="h-6 w-6 text-green-400" />
                                        )}
                                    </div>
                                    <CardDescription className="text-gray-300">
                                        {userType.description}
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-2">
                                        <h4 className="font-semibold text-sm text-gray-200 mb-3">包含功能：</h4>
                                        <ul className="space-y-2">
                                            {userType.features.map((feature, index) => (
                                                <li key={index} className="flex items-center text-sm text-gray-300">
                                                    <CheckCircle className="h-4 w-4 text-green-400 mr-2 flex-shrink-0" />
                                                    {feature}
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                </CardContent>
                            </Card>
                        );
                    })}
                </div>

                <div className="text-center">
                    <Button
                        onClick={handleConfirm}
                        disabled={!selectedType || isLoading}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg"
                        size="lg"
                    >
                        {isLoading ? '处理中...' : '确认选择'}
                    </Button>
                </div>

                {selectedType && (
                    <div className="mt-6 text-center">
                        <p className="text-gray-400 text-sm">
                            您选择了：<span className="text-white font-semibold">
                                {userTypes.find(t => t.type === selectedType)?.title}
                            </span>
                        </p>
                    </div>
                )}
            </div>
        </div>
    );
}
