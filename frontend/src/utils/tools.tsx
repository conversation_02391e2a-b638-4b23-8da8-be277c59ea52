"use client";

import {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>ontent,
    <PERSON><PERSON><PERSON>Provider,
    TooltipTrigger,
} from "@/components/animate-ui/components/tooltip";

interface AbbreviatedTextProps {
    text: string;
    maxLength: number;
}

export function AbbreviatedText({ text, maxLength }: AbbreviatedTextProps) {
    if (text?.length <= maxLength) {
        return <span>{text}</span>;
    }

    const abbreviated = `${text?.slice(0, maxLength - 3)}...`;

    return (
        <TooltipProvider>
            <Tooltip>
                <TooltipTrigger >
                    <span className="cursor-help underline">{abbreviated}</span>
                </TooltipTrigger>
                <TooltipContent>
                    <p>{text || ""}</p>
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>
    );
}
