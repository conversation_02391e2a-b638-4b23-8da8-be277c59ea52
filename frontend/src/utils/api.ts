// utils/api.ts
import axios, {
    type AxiosInstance,
    type AxiosRequestConfig,
    type AxiosResponse,
    type AxiosError,
    type InternalAxiosRequestConfig,
} from "axios";

/**
 * Axios instance with extended request config
 */
declare module "axios" {
    interface InternalAxiosRequestConfig {
        skipAuth?: boolean;
        metadata?: {
            startTime: number;
        };
        _retry?: boolean;
        retryCount?: number;
    }
}

// ==================== TYPES & INTERFACES ====================

/**
 * Standard API response wrapper
 */
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    code?: number;
    timestamp?: string;
}

/**
 * API error response structure
 */
export interface ApiError {
    message: string;
    code?: number;
    details?: any;
}

/**
 * Request configuration options
 */
export interface RequestOptions extends AxiosRequestConfig {
    skipAuth?: boolean;
    skipErrorHandler?: boolean;
    retryCount?: number;
}


// ==================== AXIOS INSTANCE CONFIGURATION ====================

/**
 * Create configured axios instance
 */
const createApiInstance = (): AxiosInstance => {
    const instance = axios.create({
        baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || "/api",
        withCredentials: true,
    });
    instance.interceptors.request.use(
        (config) => {
            if (config.skipAuth) return config;

            const getCookie = (name: string) => {
                const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));
                return match ? match[2] : null;
            };

            const githubCode = getCookie("github_code");
            const tiktokCode = getCookie("tiktok_code");

            const token = githubCode || tiktokCode;

            if (token) {
                config.headers = config.headers || {};
                config.headers.Authorization = `Bearer ${token}`;
            }

            return config;
        },
        (error) => {
            return Promise.reject(error);
        },
    );


    return instance;
};

// ==================== API CLIENT CLASS ====================

/**
 * Main API client class with comprehensive methods
 */
class ApiClient {
    private instance: AxiosInstance;

    constructor() {
        this.instance = createApiInstance();
    }

    /**
     * Generic request method with error handling
     */
    private async request<T>(config: RequestOptions): Promise<T> {
        try {
            const response = await this.instance.request<T>(config);
            return response.data;
        } catch (error) {
            return <T>error;
        }
    }
    /**
     * GET request
     */
    async get<T>(url: string, ContentType = "application/json", config?: RequestOptions): Promise<T> {
        return this.request<T>({
            headers: {
                "Content-Type": ContentType,
            },
            method: "GET",
            url,
            ...config,
        });
    }

    /**
     * POST request
     */
    async post<T>(
        url: string,
        data?: any,
        ContentType = "application/json",
        config?: RequestOptions,
    ): Promise<T> {
        return this.request<T>({
            headers: {
                "Content-Type": ContentType,
            },
            method: "POST",
            url,
            data,
            ...config,
        });
    }
}


/**
 * Create resource-specific API service
 */
export const createApiService = (baseUrl: string) => {
    const client = new ApiClient();

    return {
        client,
        baseUrl,
        // Provide access to all HTTP methods with full flexibility
        get: <T>(path = "", contentType = "application/json", config?: RequestOptions) => {
            const url = path ? `${baseUrl}/${path}` : baseUrl;
            return client.get<T>(url, contentType, config);
        },

        post: <T>(path = "", data?: any, contentType = "application/json", config?: RequestOptions) => {
            const url = path ? `${baseUrl}/${path}` : baseUrl;
            return client.post<T>(url, data, contentType, config);
        }
    };
};

export const api = new ApiClient();

// Export specific service creators
export const tkCommentsService = createApiService("/tk_comments");
export const tkProfileService = createApiService("/tk_profile");
export const tkVideoWithHashTagsService = createApiService("/tk_video_with_hashtags");
export const tkAIAgentService = createApiService("/ai_agent");
export const tkAuthorIDsService = createApiService("/author_ids");
export const tkAnalyticsService = createApiService("/analytics");



