export interface TKComment {
    avatarThumbnail: string
    cid: string
    createTime: number
    createTimeISO: string
    detailedMentions: any[]
    diggCount: number
    input: string
    likedByAuthor: boolean
    mentions: any[]
    pinnedByAuthor: boolean
    repliesToId: any
    replyCommentTotal: number
    submittedVideoUrl: string
    text: string
    uid: string
    uniqueId: string
    videoWebUrl: string
}

export interface TKCommentFromAPI {
    id: number
    user_id: number
    video_url: string
    comment_id: string
    comment_text: string
    author_uid: string
    author_unique_id: string
    avatar_url: string
    digg_count: number
    liked_by_author: boolean
    pinned_by_author: boolean
    reply_comment_total: number
    created_at: string
    comment_time: string
    expires_at: string
}


// tiktok profile data structure
// -----------------------------------------------------------------

export interface TKProfileFromAPI {
    video_id: string
    video_text: string
    text_language: string
    video_url: string
    cover_url: string
    create_time: string
    duration: number
    definition: any
    comment_count: number
    digg_count: number
    share_count: number
    play_count: number
    collect_count: number
    is_ad: boolean
    is_pinned: boolean
    is_sponsored: boolean
    is_slideshow: boolean
    user_id: number
    expires_at: string
    created_at: string
    author_id: string
    author_username: string
    author_nickname: string
    author_avatar_url: string
    author_signature: string
    music_id: string
    music_name: string
    music_author: string
    music_play_url: string
}


export interface TKProfile {
    authorMeta: AuthorMeta
    collectCount: number
    commentCount: number
    createTime: number
    createTimeISO: string
    detailedMentions: any[]
    diggCount: number
    effectStickers: EffectSticker[]
    fromProfileSection: string
    hashtags: Hashtag[]
    id: string
    input: string
    isAd: boolean
    isPinned: boolean
    isSlideshow: boolean
    isSponsored: boolean
    mediaUrls: any[]
    mentions: any[]
    musicMeta: MusicMeta
    playCount: number
    shareCount: number
    text: string
    textLanguage: string
    videoMeta: VideoMeta
    webVideoUrl: string
}

export interface AuthorMeta {
    avatar: string
    bioLink: any
    commerceUserInfo: CommerceUserInfo
    digg: number
    fans: number
    following: number
    friends: number
    heart: number
    id: string
    name: string
    nickName: string
    originalAvatarUrl: string
    privateAccount: boolean
    profileUrl: string
    region: string
    roomId: string
    signature: string
    ttSeller: boolean
    verified: boolean
    video: number
}

export interface CommerceUserInfo {
    commerceUser: boolean
}

export interface EffectSticker {
    ID: string
    name: string
    stickerStats: StickerStats
}

export interface StickerStats {
    useCount: number
}

export interface Hashtag {
    name: string
}

export interface MusicMeta {
    coverMediumUrl: string
    musicAuthor: string
    musicId: string
    musicName: string
    musicOriginal: boolean
    originalCoverMediumUrl: string
    playUrl: string
}

export interface VideoMeta {
    coverUrl: string
    definition: string
    duration: number
    format: string
    height: number
    originalCoverUrl: string
    width: number
}


// tiktok video with hashTags data structure
// -----------------------------------------------------------------

export interface TKVideoWithHashTagsFromAPI {
    id: string
    text: string
    text_language: string
    user_id: number
    video_url: string
    cover_url: any
    width: any
    height: any
    duration: any
    definition: any
    create_time: string
    create_time_iso: string
    input: string
    search_hashtag: string
    search_hashtag_views: number
    comment_count: number
    digg_count: number
    share_count: number
    play_count: number
    collect_count: number
    is_ad: boolean
    is_pinned: boolean
    is_sponsored: boolean
    is_slideshow: boolean
    expires_at: string
    created_at: string
    author_id: string
    username: string
    nickname: string
    profile_url: string
    avatar_url: string
    original_avatar_url: string
    signature: string
    bio_link: any
    verified: boolean
    private_account: boolean
    region: any
    fans: number
    heart: number
    following: number
    friends: number
    video_count: number
    author_digg_count: number
    music_id: string
    music_name: string
    music_author: string
    is_original: boolean
    play_url: string
    music_cover_url: string
    original_cover_url: string
}
export interface TKVideoWithHashTags {
    authorMeta: AuthorMetaWithTag
    collectCount: number
    commentCount: number
    createTime: number
    createTimeIso: string
    detailedMentions: any[]
    diggCount: number
    effectStickers: any[]
    hashtags: Hashtag[]
    id: string
    input: string
    isAd: boolean
    isMuted: boolean
    isPinned: boolean
    isSlideshow: boolean
    isSponsored: boolean
    mediaUrls: any[]
    mentions: any[]
    musicMeta: MusicMeta
    playCount: number
    searchHashtag: SearchHashtag
    shareCount: number
    text: string
    textLanguage: string
    videoMeta: VideoMeta
    webVideoUrl: string
}


export interface AuthorMetaWithTag {
    avatar: string
    bioLink: any
    digg: number
    fans: number
    following: number
    friends: number
    heart: number
    id: string
    name: string
    nickName: string
    originalAvatarUrl: string
    privateAccount: boolean
    profileUrl: string
    signature: string
    ttSeller: boolean
    verified: boolean
    video: number
}

export interface Hashtag {
    cover: string
    id: string
    name: string
    title: string
}

export interface MusicMeta {
    coverMediumUrl: string
    musicAlbum: string
    musicAuthor: string
    musicId: string
    musicName: string
    musicOriginal: boolean
    originalCoverMediumUrl: string
    playUrl: string
}

export interface SearchHashtag {
    name: string
    views: number
}

export interface VideoMeta {
    coverUrl: string
    definition: string
    duration: number
    format: string
    height: number
    originalCoverUrl: string
    width: number
}



// User Types
// -------------------------------
export type UserType = 'personal' | 'business';

export interface UserPermissions {
    can_analyze_url: boolean;
    can_view_basic_metrics: boolean;
    can_use_ai_analysis: boolean;
    can_view_competitor_analysis: boolean;
    can_view_roi_analysis: boolean;
    can_view_advanced_metrics: boolean;
    can_access_business_api: boolean;
    can_view_ad_insights: boolean;
    can_export_data: boolean;
}

export interface DashboardConfig {
    show_competitor_analysis: boolean;
    show_roi_metrics: boolean;
    show_advanced_charts: boolean;
    available_features: string[];
    max_analysis_requests: number;
}

export interface UserTypeResponse {
    user_type: UserType;
    permissions: UserPermissions;
}

// -------------------------------
export interface GithubUserInfo {
    id: number
    github_id: number
    login: string
    email: any
    name: string
    avatar_url: string
    token: string
    user_type: UserType
    author_unique_ids: string[]
    daily_analysis_count: number
    last_analysis_date: string
    created_at: string
    updated_at: string
}


// -------------------------------
export interface AnalyticsComments {
    id: number
    video_url: string
    user_id: number
    text: string
    intent_score: number
    labels: string[]
    urls: string[]
    final_score: number
    scenario_reinforcement: string
    created_at: string
}

export interface AnalyticsKocs {
    id: number
    video_url: string
    user_id: number
    intent_score: number
    labels: string[]
    urls: string[]
    final_score: number
    scenario_reinforcement: string
    created_at: string
}
