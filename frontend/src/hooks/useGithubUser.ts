"use client";

import type { GithubUserInfo } from "@/utils/type";
import { useEffect, useState } from "react";

export function useTikTokUser() {
    const [loading, setLoading] = useState(false);
    const [userInfo, setUserInfo] = useState<GithubUserInfo>();
    const [error, setError] = useState<string | null>(null);


    function getCookie(name: string) {
        const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));
        if (match) return match[2];
        return null;
    }

    useEffect(() => {
        setLoading(true);

        fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/tiktok/user`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                "Authorization": "Bearer " + getCookie("tiktok_code")
            },
            credentials: "include",
        })
            .then((res) => res.json())
            .then((data) => {
                if (data.error) {
                    setError(data.error);
                } else {
                    setUserInfo(data.user);
                }
            })
            .catch((err) => {
                setError("Failed to fetch user info.");
                console.error(err);
            })
            .finally(() => {
                setLoading(false);
            });
    }, []);

    return { loading, userInfo, error };
}
