import { NextRequest, NextResponse } from 'next/server'

export function middleware(request: NextRequest) {

    const { pathname } = request.nextUrl;

    const token = request.cookies.get('tiktok_code')?.value;

    if (!token && pathname !== '/login') {
        const url = request.nextUrl.clone();
        url.pathname = '/login';
        return NextResponse.redirect(url);
    }
    if (token && pathname === '/') {
        const url = request.nextUrl.clone();
        url.pathname = '/dashboard';
        return NextResponse.redirect(url);
    }
    if (token && pathname === '/login') {
        const url = request.nextUrl.clone();
        url.pathname = '/dashboard';
        return NextResponse.redirect(url);
    }
    if (token && pathname === '/auth/tiktok/finalize') {
        const url = request.nextUrl.clone();
        url.pathname = '/dashboard';
        url.search = '';
        return NextResponse.redirect(url);
    }

    return NextResponse.next();
}

export const config = {
    matcher: [
        '/((?!api|_next/static|_next/image|favicon.ico|.*\\..*$).*)',
    ],
}