{"name": "frontend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "biome check .", "check:unsafe": "biome check --write --unsafe .", "check:write": "biome check --write .", "dev": "next dev --turbo", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit"}, "dependencies": {"@auth/core": "^0.40.0", "@auth/drizzle-adapter": "^1.10.0", "@headlessui/react": "^2.2.4", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@t3-oss/env-nextjs": "^0.12.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "drizzle-orm": "^0.44.2", "lucide-react": "^0.525.0", "motion": "^12.23.0", "next": "^15.2.3", "next-auth": "5.0.0-beta.29", "pg": "^8.16.3", "postgres": "^3.4.7", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "tailwind-merge": "^3.3.1", "zod": "^3.24.2"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@tailwindcss/postcss": "^4.0.15", "@types/node": "^20.14.10", "@types/pg": "^8.15.4", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "drizzle-kit": "^0.31.4", "postcss": "^8.5.3", "tailwindcss": "^4.0.15", "tw-animate-css": "^1.3.5", "typescript": "^5.8.2"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "packageManager": "pnpm@10.11.1"}